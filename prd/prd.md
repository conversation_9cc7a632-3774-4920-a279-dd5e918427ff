## 背景
* 需求
* 需要一个专业的调音app，他能精准的识别吉他调音的第几弦，界面尽量的简约易用。能够清晰的判断出，音准高低。

* 定位：垂直于吉他调音的APP，能够快速方便简易的使用。目前只服务于苹果手机

* 名称：吉他调音易


## 功能要求
1、简易调音
* 这个调音APP，只服务于吉他调音，包括电吉他和木吉他调音，可以选择标准音和升降音,单次最小半个音。

2、专业调音
* 显示一个频率仪，中间有一个竖线，在调音的时候，频率围绕他左右变动，如果当前声音的频率属于，六根弦的之上，则标尺与这个竖线重叠，否则如果低于这个频率着靠左，高于则靠右。

3、我的，用于登录界面记录自己的调音配置。

## 吉他背景知识
* 吉他的标准音有（E、B、G、D、E），分别代表第一到第六根弦。


## 交互要求
* 打开app，自动进入快速调音界面。
标准音和升降音通过下拉选择，如果有更好的交互可以你可以提供建议。


## 界面布局
* 下面有两个tab，1、快速调音 2、专业调音 3、我的（查看登录情况等）
* 快速调音，上面有一个频率一起，下半部分是一个吉他头的形状，显示六个旋钮。
* 专业调音就只有一个频率仪器，并帮我美化他。



## 其他要求
* 这个APP需要拾取手机的麦克风，同时为了更加精准调音，实现一定的降噪能力。和算法优化调音的精准度。

* APP可以无需登录使用，如果没有登录，数据存储在APP客户端。

* 通过专业的app分析能力，提供功能和交互的优化建议，使得更多的用户喜欢使用当前APP。

* 使用苹果原生开发

