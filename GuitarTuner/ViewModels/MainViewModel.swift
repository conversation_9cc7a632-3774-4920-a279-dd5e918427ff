import Foundation
import Combine

class MainViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var selectedTab: Tab = .quickTuning
    @Published var currentTuning: GuitarTuning = .standard
    @Published var isRecording = false
    @Published var showSettings = false
    
    // MARK: - Services
    let audioManager: AudioManager
    let tuningAnalyzer: TuningAnalyzer
    let settingsManager: SettingsManager
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init() {
        self.audioManager = AudioManager()
        self.tuningAnalyzer = TuningAnalyzer(audioManager: audioManager)
        self.settingsManager = SettingsManager()
        
        setupBindings()
        loadInitialSettings()
    }
    
    // MARK: - Setup
    private func setupBindings() {
        // 监听录音状态
        audioManager.$isRecording
            .assign(to: \.isRecording, on: self)
            .store(in: &cancellables)
        
        // 监听调音设置变化
        settingsManager.$settings
            .map(\.preferredTuning)
            .assign(to: \.currentTuning, on: self)
            .store(in: &cancellables)
        
        // 更新调音分析器
        $currentTuning
            .sink { [weak self] tuning in
                self?.tuningAnalyzer.setTuning(tuning)
            }
            .store(in: &cancellables)
    }
    
    private func loadInitialSettings() {
        currentTuning = settingsManager.settings.preferredTuning
    }
    
    // MARK: - Public Methods
    func startTuning() {
        audioManager.startRecording()
    }
    
    func stopTuning() {
        audioManager.stopRecording()
    }
    
    func toggleTuning() {
        if isRecording {
            stopTuning()
        } else {
            startTuning()
        }
    }
    
    func setTuning(_ tuning: GuitarTuning) {
        currentTuning = tuning
        settingsManager.updateSettings(UserSettings(
            preferredTuning: tuning,
            sensitivity: settingsManager.settings.sensitivity,
            autoDetect: settingsManager.settings.autoDetect,
            soundEnabled: settingsManager.settings.soundEnabled,
            vibrationEnabled: settingsManager.settings.vibrationEnabled
        ))
    }
    
    func saveTuningSession() {
        guard let result = tuningAnalyzer.getTuningResult() else { return }
        
        let accuracy: [GuitarString: TuningAccuracy] = [
            result.detectedString ?? .first: result.accuracy
        ]
        
        let history = TuningHistory(
            tuning: currentTuning,
            accuracy: accuracy,
            duration: 30.0 // 假设调音会话持续30秒
        )
        
        settingsManager.addTuningHistory(history)
    }
    
    // MARK: - Tab Management
    func selectTab(_ tab: Tab) {
        selectedTab = tab
    }
    
    // MARK: - Settings
    func updateSettings(_ settings: UserSettings) {
        settingsManager.updateSettings(settings)
    }
    
    func getCurrentSettings() -> UserSettings {
        return settingsManager.settings
    }
    
    // MARK: - Statistics
    func getStatistics() -> TuningStatistics {
        return settingsManager.getTuningStatistics()
    }
}

// MARK: - Tab Enum
enum Tab: Int, CaseIterable {
    case quickTuning = 0
    case professionalTuning = 1
    case profile = 2
    
    var title: String {
        switch self {
        case .quickTuning: return "快速调音"
        case .professionalTuning: return "专业调音"
        case .profile: return "我的"
        }
    }
    
    var icon: String {
        switch self {
        case .quickTuning: return "music.note"
        case .professionalTuning: return "waveform"
        case .profile: return "person.circle"
        }
    }
} 