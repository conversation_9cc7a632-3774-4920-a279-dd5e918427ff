import Foundation
import Combine

class SettingsManager: ObservableObject {
    // MARK: - Published Properties
    @Published var settings: UserSettings {
        didSet {
            saveSettings()
        }
    }
    
    @Published var tuningHistory: [TuningHistory] = [] {
        didSet {
            saveTuningHistory()
        }
    }
    
    // MARK: - Private Properties
    private let settingsKey = "UserSettings"
    private let historyKey = "TuningHistory"
    private let maxHistoryCount = 100
    
    // MARK: - Initialization
    init() {
        self.settings = Self.loadSettings()
        self.tuningHistory = Self.loadTuningHistory()
    }
    
    // MARK: - Settings Management
    func updateSettings(_ newSettings: UserSettings) {
        settings = newSettings
    }
    
    func resetSettings() {
        settings = UserSettings.default
    }
    
    private func saveSettings() {
        if let encoded = try? JSONEncoder().encode(settings) {
            UserDefaults.standard.set(encoded, forKey: settingsKey)
        }
    }
    
    private static func loadSettings() -> UserSettings {
        guard let data = UserDefaults.standard.data(forKey: "UserSettings"),
              let settings = try? JSONDecoder().decode(UserSettings.self, from: data) else {
            return UserSettings.default
        }
        return settings
    }
    
    // MARK: - Tuning History Management
    func addTuningHistory(_ history: TuningHistory) {
        tuningHistory.append(history)
        
        // 限制历史记录数量
        if tuningHistory.count > maxHistoryCount {
            tuningHistory.removeFirst(tuningHistory.count - maxHistoryCount)
        }
    }
    
    func clearTuningHistory() {
        tuningHistory.removeAll()
    }
    
    func removeTuningHistory(at indexSet: IndexSet) {
        tuningHistory.remove(atOffsets: indexSet)
    }
    
    private func saveTuningHistory() {
        if let encoded = try? JSONEncoder().encode(tuningHistory) {
            UserDefaults.standard.set(encoded, forKey: historyKey)
        }
    }
    
    private static func loadTuningHistory() -> [TuningHistory] {
        guard let data = UserDefaults.standard.data(forKey: "TuningHistory"),
              let history = try? JSONDecoder().decode([TuningHistory].self, from: data) else {
            return []
        }
        return history
    }
    
    // MARK: - Statistics
    func getTuningStatistics() -> TuningStatistics {
        let totalSessions = tuningHistory.count
        let perfectTunings = tuningHistory.filter { history in
            history.accuracy.values.allSatisfy { $0 == .perfect }
        }.count
        
        let averageDuration = tuningHistory.isEmpty ? 0 : 
            tuningHistory.reduce(0) { $0 + $1.duration } / Double(tuningHistory.count)
        
        let mostUsedTuning = tuningHistory
            .map { $0.tuning }
            .reduce(into: [:]) { counts, tuning in
                counts[tuning, default: 0] += 1
            }
            .max(by: { $0.value < $1.value })?.key ?? .standard
        
        return TuningStatistics(
            totalSessions: totalSessions,
            perfectTunings: perfectTunings,
            averageDuration: averageDuration,
            mostUsedTuning: mostUsedTuning
        )
    }
}

// MARK: - Tuning Statistics
struct TuningStatistics {
    let totalSessions: Int
    let perfectTunings: Int
    let averageDuration: TimeInterval
    let mostUsedTuning: GuitarTuning
    
    var perfectPercentage: Double {
        guard totalSessions > 0 else { return 0.0 }
        return Double(perfectTunings) / Double(totalSessions) * 100.0
    }
    
    var averageDurationString: String {
        let minutes = Int(averageDuration) / 60
        let seconds = Int(averageDuration) % 60
        return String(format: "%d分%d秒", minutes, seconds)
    }
} 