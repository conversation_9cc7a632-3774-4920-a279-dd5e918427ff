import Foundation
import Combine
import AVFoundation
import Accelerate

class AudioManager: NSObject, ObservableObject {
    // MARK: - Published Properties
    @Published var currentFrequency: Float = 0.0
    @Published var currentAmplitude: Float = 0.0
    @Published var isRecording = false
    @Published var errorMessage: String?
    @Published var permissionGranted = false

    // MARK: - Private Properties
    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?
    private var audioSession: AVAudioSession?

    // FFT相关属性
    private let fftSize = 4096
    private let sampleRate: Float = 44100.0
    private let bufferSize = 1024
    private var fftSetup: FFTSetup?
    private var window: [Float] = []
    private var audioBuffer: [Float] = []

    // 频率分析相关
    private let updateInterval: TimeInterval = 0.01 // 10毫秒
    private var analysisTimer: Timer?

    // 音频滤波相关
    private var highPassFilter: HighPassFilter
    private let noiseThreshold: Float = 0.0001
    private let smoothingFactor: Float = 0.3
    private var previousFrequency: Float = 0.0
    private var previousAmplitude: Float = 0.0

    // MARK: - Initialization
    override init() {
        self.highPassFilter = HighPassFilter(cutoffFrequency: 80.0, sampleRate: sampleRate)
        super.init()
        setupFFT()
        setupAudioSession()
        requestMicrophonePermission()
    }

    deinit {
        stopRecording()
        if let fftSetup = fftSetup {
            vDSP_destroy_fftsetup(fftSetup)
        }
    }

    // MARK: - Setup Methods
    private func setupFFT() {
        let log2n = vDSP_Length(log2(Float(fftSize)))
        fftSetup = vDSP_create_fftsetup(log2n, Int32(kFFTRadix2))

        // 创建汉宁窗
        window = Array(repeating: 0.0, count: fftSize)
        vDSP_hann_window(&window, vDSP_Length(fftSize), Int32(vDSP_HANN_NORM))

        audioBuffer = Array(repeating: 0.0, count: fftSize)
    }

    private func setupAudioSession() {
        audioSession = AVAudioSession.sharedInstance()

        do {
            // 设置音频会话类别和模式 - 录音模式只使用兼容的选项
            try audioSession?.setCategory(.record, mode: .measurement, options: [.allowBluetooth])

            // 尝试设置首选采样率，但不强制要求
            try? audioSession?.setPreferredSampleRate(Double(sampleRate))

            // 设置缓冲区持续时间
            let bufferDuration = Double(bufferSize) / Double(sampleRate)
            try? audioSession?.setPreferredIOBufferDuration(bufferDuration)

        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "音频会话设置失败: \(error.localizedDescription)"
            }
        }
    }

    private func requestMicrophonePermission() {
        if #available(iOS 17.0, *) {
            AVAudioApplication.requestRecordPermission { [weak self] granted in
                DispatchQueue.main.async {
                    self?.permissionGranted = granted
                    if !granted {
                        self?.errorMessage = "需要麦克风权限才能进行调音"
                    }
                }
            }
        } else {
            AVAudioSession.sharedInstance().requestRecordPermission { [weak self] granted in
                DispatchQueue.main.async {
                    self?.permissionGranted = granted
                    if !granted {
                        self?.errorMessage = "需要麦克风权限才能进行调音"
                    }
                }
            }
        }
    }

    // MARK: - Public Methods
    func startRecording() {
        guard !isRecording else { return }
        guard permissionGranted else {
            errorMessage = "请先授予麦克风权限"
            return
        }

        do {
            // 先激活音频会话
            try audioSession?.setActive(true)

            // 然后设置音频引擎
            setupAudioEngine()

            // 启动音频引擎
            try audioEngine?.start()

            DispatchQueue.main.async {
                self.isRecording = true
                self.errorMessage = nil
                print("音频录制已启动")
            }

            // 启动分析定时器
            startAnalysisTimer()

        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "音频引擎启动失败: \(error.localizedDescription)"
                print("音频启动错误: \(error)")
            }
        }
    }

    func stopRecording() {
        guard isRecording else { return }

        // 停止分析定时器
        analysisTimer?.invalidate()
        analysisTimer = nil

        // 停止音频引擎
        audioEngine?.stop()
        audioEngine?.inputNode.removeTap(onBus: 0)

        // 重置滤波器
        highPassFilter.reset()
        previousFrequency = 0.0
        previousAmplitude = 0.0

        // 停用音频会话
        do {
            try audioSession?.setActive(false, options: .notifyOthersOnDeactivation)
        } catch {
            print("停止音频会话失败: \(error.localizedDescription)")
        }

        DispatchQueue.main.async {
            self.isRecording = false
            self.currentFrequency = 0.0
            self.currentAmplitude = 0.0
            print("音频录制已停止")
        }
    }

    // MARK: - Private Audio Engine Methods
    private func setupAudioEngine() {
        audioEngine = AVAudioEngine()
        inputNode = audioEngine?.inputNode

        guard let inputNode = inputNode else {
            DispatchQueue.main.async {
                self.errorMessage = "无法获取音频输入节点"
            }
            return
        }

        // 使用输入节点的原始格式，而不是强制设置格式
        let inputFormat = inputNode.outputFormat(forBus: 0)

        // 如果需要转换格式，使用转换器
        let desiredFormat = AVAudioFormat(standardFormatWithSampleRate: Double(sampleRate), channels: 1)

        guard let desiredFormat = desiredFormat else {
            DispatchQueue.main.async {
                self.errorMessage = "无法创建目标格式"
            }
            return
        }

        // 检查是否需要格式转换
        if inputFormat.sampleRate != desiredFormat.sampleRate || inputFormat.channelCount != desiredFormat.channelCount {
            // 使用格式转换器
            setupAudioEngineWithConverter(inputNode: inputNode, inputFormat: inputFormat, outputFormat: desiredFormat)
        } else {
            // 直接使用输入格式
            inputNode.installTap(onBus: 0, bufferSize: AVAudioFrameCount(bufferSize), format: inputFormat) { [weak self] buffer, _ in
                self?.processAudioBuffer(buffer)
            }
        }
    }

    private func setupAudioEngineWithConverter(inputNode: AVAudioInputNode, inputFormat: AVAudioFormat, outputFormat: AVAudioFormat) {
        let converter = AVAudioConverter(from: inputFormat, to: outputFormat)

        guard let audioConverter = converter else {
            DispatchQueue.main.async {
                self.errorMessage = "无法创建音频转换器"
            }
            return
        }

        inputNode.installTap(onBus: 0, bufferSize: AVAudioFrameCount(bufferSize), format: inputFormat) { [weak self] buffer, _ in
            // 创建输出缓冲区
            guard let outputBuffer = AVAudioPCMBuffer(pcmFormat: outputFormat, frameCapacity: buffer.frameLength) else {
                return
            }

            var error: NSError?
            let status = audioConverter.convert(to: outputBuffer, error: &error) { _, outStatus in
                outStatus.pointee = .haveData
                return buffer
            }

            if status == .error {
                print("音频转换错误: \(error?.localizedDescription ?? "未知错误")")
                return
            }

            self?.processAudioBuffer(outputBuffer)
        }
    }

    private func startAnalysisTimer() {
        analysisTimer = Timer.scheduledTimer(withTimeInterval: updateInterval, repeats: true) { [weak self] _ in
            self?.analyzeFrequency()
        }
    }

    private func processAudioBuffer(_ buffer: AVAudioPCMBuffer) {
        guard let channelData = buffer.floatChannelData?[0] else { return }
        let frameCount = Int(buffer.frameLength)

        // 确保frameCount有效
        guard frameCount > 0 else { return }

        // 将新的音频数据添加到缓冲区
        audioBuffer.removeFirst(min(frameCount, audioBuffer.count))

        for i in 0..<frameCount {
            // 应用高通滤波器去除低频噪音
            let filteredSample = highPassFilter.process(channelData[i])
            audioBuffer.append(filteredSample)
        }

        // 保持缓冲区大小
        while audioBuffer.count > fftSize {
            audioBuffer.removeFirst()
        }
    }

    private func analyzeFrequency() {
        guard audioBuffer.count >= fftSize else { return }

        // 准备FFT输入数据
        var inputReal = Array(audioBuffer.suffix(fftSize))
        var inputImag = Array(repeating: Float(0.0), count: fftSize)

        // 应用窗函数
        vDSP_vmul(inputReal, 1, window, 1, &inputReal, 1, vDSP_Length(fftSize))

        // 执行FFT
        inputReal.withUnsafeMutableBufferPointer { realPtr in
            inputImag.withUnsafeMutableBufferPointer { imagPtr in
                var splitComplex = DSPSplitComplex(realp: realPtr.baseAddress!, imagp: imagPtr.baseAddress!)

                guard let fftSetup = fftSetup else { return }
                let log2n = vDSP_Length(log2(Float(fftSize)))
                vDSP_fft_zrip(fftSetup, &splitComplex, 1, log2n, Int32(FFT_FORWARD))

                // 计算幅度谱
                var magnitudes = Array(repeating: Float(0.0), count: fftSize/2)
                vDSP_zvmags(&splitComplex, 1, &magnitudes, 1, vDSP_Length(fftSize/2))

                // 找到峰值频率
                let (rawFrequency, rawAmplitude) = findPeakFrequency(magnitudes: magnitudes)

                // 应用平滑处理
                let smoothedFrequency = self.smoothFrequency(rawFrequency)
                let smoothedAmplitude = self.smoothAmplitude(rawAmplitude)

                DispatchQueue.main.async {
                    self.currentFrequency = smoothedFrequency
                    self.currentAmplitude = smoothedAmplitude

                    // 调试输出
                    if smoothedFrequency > 0 {
                        print("检测到频率: \(smoothedFrequency) Hz, 幅度: \(smoothedAmplitude)")
                    }
                }
            }
        }
    }

    private func findPeakFrequency(magnitudes: [Float]) -> (frequency: Float, amplitude: Float) {
        // 扩大频率分析范围 (50Hz - 1000Hz) 以便更好地检测各种声音
        let minFreq: Float = 50.0
        let maxFreq: Float = 1000.0
        let freqResolution = sampleRate / Float(fftSize)

        let minBin = Int(minFreq / freqResolution)
        let maxBin = Int(maxFreq / freqResolution)

        guard minBin < magnitudes.count && maxBin < magnitudes.count else {
            return (0.0, 0.0)
        }

        // 找到最大幅度的频率bin
        var maxMagnitude: Float = 0.0
        var maxBinIndex = minBin

        for i in minBin...maxBin {
            if magnitudes[i] > maxMagnitude {
                maxMagnitude = magnitudes[i]
                maxBinIndex = i
            }
        }

        // 使用抛物线插值提高频率精度
        let frequency = interpolateFrequency(bin: maxBinIndex, magnitudes: magnitudes, freqResolution: freqResolution)
        let amplitude = sqrt(maxMagnitude) / Float(fftSize) * 2.0

        // 自适应阈值：基于平均幅度动态调整
        let averageAmplitude = magnitudes.reduce(0, +) / Float(magnitudes.count)
        let adaptiveThreshold = max(noiseThreshold, averageAmplitude * 0.1)

        if amplitude < adaptiveThreshold {
            return (0.0, 0.0)
        }

        return (frequency, amplitude)
    }

    private func interpolateFrequency(bin: Int, magnitudes: [Float], freqResolution: Float) -> Float {
        guard bin > 0 && bin < magnitudes.count - 1 else {
            return Float(bin) * freqResolution
        }

        let y1 = magnitudes[bin - 1]
        let y2 = magnitudes[bin]
        let y3 = magnitudes[bin + 1]

        // 抛物线插值
        let a = (y1 - 2*y2 + y3) / 2
        let b = (y3 - y1) / 2

        var binOffset: Float = 0.0
        if abs(a) > 0.0001 {
            binOffset = -b / (2 * a)
            binOffset = max(-0.5, min(0.5, binOffset))
        }

        return (Float(bin) + binOffset) * freqResolution
    }

    private func smoothFrequency(_ newFrequency: Float) -> Float {
        guard newFrequency > 0 else { return 0.0 }

        if previousFrequency == 0.0 {
            previousFrequency = newFrequency
            return newFrequency
        }

        // 如果频率变化太大，可能是噪音，使用更强的平滑
        let frequencyDiff = abs(newFrequency - previousFrequency)
        let adaptiveSmoothingFactor = frequencyDiff > 50.0 ? smoothingFactor * 2.0 : smoothingFactor

        let smoothed = previousFrequency * adaptiveSmoothingFactor + newFrequency * (1.0 - adaptiveSmoothingFactor)
        previousFrequency = smoothed
        return smoothed
    }

    private func smoothAmplitude(_ newAmplitude: Float) -> Float {
        if previousAmplitude == 0.0 {
            previousAmplitude = newAmplitude
            return newAmplitude
        }

        let smoothed = previousAmplitude * smoothingFactor + newAmplitude * (1.0 - smoothingFactor)
        previousAmplitude = smoothed
        return smoothed
    }

    // MARK: - Frequency Analysis
    func detectGuitarString(frequency: Float, tuning: GuitarTuning) -> GuitarString? {
        let frequencies = tuning.frequencies

        for (string, targetFreq) in frequencies {
            let tolerance = targetFreq * 0.1 // 10% 容差
            if abs(frequency - targetFreq) <= tolerance {
                return string
            }
        }

        return nil
    }

    func calculateTuningAccuracy(targetFreq: Float, actualFreq: Float) -> TuningAccuracy {
        guard actualFreq > 0 && targetFreq > 0 else { return .outOfRange }

        let cents = 1200 * log2(actualFreq / targetFreq)

        if abs(cents) < 10 {
            return .perfect
        } else if cents < 0 {
            return .tooLow
        } else {
            return .tooHigh
        }
    }

    // MARK: - Utility Methods
    func getFrequencyHistory() -> [AudioData] {
        return []
    }

    func clearHistory() {
        // 清空历史记录
    }
}

// MARK: - High Pass Filter
class HighPassFilter {
    private let cutoffFrequency: Float
    private let sampleRate: Float
    private var previousInput: Float = 0.0
    private var previousOutput: Float = 0.0
    private let alpha: Float

    init(cutoffFrequency: Float, sampleRate: Float) {
        self.cutoffFrequency = cutoffFrequency
        self.sampleRate = sampleRate

        // 计算滤波器系数
        let rc = 1.0 / (2.0 * Float.pi * cutoffFrequency)
        let dt = 1.0 / sampleRate
        self.alpha = rc / (rc + dt)
    }

    func process(_ input: Float) -> Float {
        // 高通滤波器实现
        let output = alpha * (previousOutput + input - previousInput)

        previousInput = input
        previousOutput = output

        return output
    }

    func reset() {
        previousInput = 0.0
        previousOutput = 0.0
    }
}