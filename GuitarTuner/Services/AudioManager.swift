import Foundation
import Combine

class AudioManager: ObservableObject {
    // MARK: - Published Properties
    @Published var currentFrequency: Float = 0.0
    @Published var currentAmplitude: Float = 0.0
    @Published var isRecording = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private var timer: Timer?
    
    // MARK: - Public Methods
    func startRecording() {
        guard !isRecording else { return }
        
        isRecording = true
        errorMessage = nil
        
        // 模拟音频数据
        timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            self?.simulateAudioData()
        }
    }
    
    func stopRecording() {
        guard isRecording else { return }
        
        timer?.invalidate()
        timer = nil
        
        isRecording = false
        currentFrequency = 0.0
        currentAmplitude = 0.0
    }
    
    // MARK: - Private Methods
    private func simulateAudioData() {
        // 模拟吉他弦的频率数据
        let guitarFrequencies: [Float] = [329.63, 246.94, 196.00, 146.83, 110.00, 82.41]
        let randomIndex = Int.random(in: 0..<guitarFrequencies.count)
        let baseFrequency = guitarFrequencies[randomIndex]
        
        // 添加一些随机变化
        let variation = Float.random(in: -10...10)
        currentFrequency = baseFrequency + variation
        currentAmplitude = Float.random(in: 0.1...1.0)
    }
    
    // MARK: - Frequency Analysis
    func detectGuitarString(frequency: Float, tuning: GuitarTuning) -> GuitarString? {
        let frequencies = tuning.frequencies
        
        for (string, targetFreq) in frequencies {
            let tolerance = targetFreq * 0.1 // 10% 容差
            if abs(frequency - targetFreq) <= tolerance {
                return string
            }
        }
        
        return nil
    }
    
    func calculateTuningAccuracy(targetFreq: Float, actualFreq: Float) -> TuningAccuracy {
        guard actualFreq > 0 && targetFreq > 0 else { return .outOfRange }
        
        let cents = 1200 * log2(actualFreq / targetFreq)
        
        if abs(cents) < 10 {
            return .perfect
        } else if cents < 0 {
            return .tooLow
        } else {
            return .tooHigh
        }
    }
    
    // MARK: - Utility Methods
    func getFrequencyHistory() -> [AudioData] {
        return []
    }
    
    func clearHistory() {
        // 清空历史记录
    }
} 