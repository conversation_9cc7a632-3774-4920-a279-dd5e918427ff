import SwiftUI

struct ProfessionalTuningView: View {
    @ObservedObject var viewModel: MainViewModel
    @State private var showSettings = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 标题和设置按钮
                HStack {
                    Text("专业调音")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Spacer()
                    
                    Button(action: {
                        showSettings = true
                    }) {
                        Image(systemName: "gearshape.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                }
                .padding(.horizontal)
                
                // 频率显示
                FrequencyDisplayView(
                    frequency: viewModel.audioManager.currentFrequency,
                    amplitude: viewModel.audioManager.currentAmplitude
                )
                
                // 专业调音仪表
                ProfessionalTunerMeterView(
                    frequency: viewModel.audioManager.currentFrequency,
                    targetFrequency: getTargetFrequency(),
                    amplitude: viewModel.audioManager.currentAmplitude,
                    tuningAccuracy: viewModel.tuningAnalyzer.tuningAccuracy,
                    cents: viewModel.tuningAnalyzer.cents
                )
                
                // 当前检测到的弦信息
                if let detectedString = viewModel.tuningAnalyzer.detectedString {
                    StringInfoView(
                        string: detectedString,
                        tuning: viewModel.currentTuning,
                        accuracy: viewModel.tuningAnalyzer.tuningAccuracy
                    )
                }
                
                Spacer()
                
                // 控制按钮
                ControlButtonsView(
                    isRecording: viewModel.isRecording,
                    onToggleTuning: viewModel.toggleTuning,
                    onShowTuningSelector: { showSettings = true }
                )
            }
            .padding()
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showSettings) {
            SettingsView(viewModel: viewModel)
        }
    }
    
    private func getTargetFrequency() -> Float {
        guard let detectedString = viewModel.tuningAnalyzer.detectedString else {
            return 0.0
        }
        return viewModel.currentTuning.frequencies[detectedString] ?? 0.0
    }
}

// MARK: - Professional Tuner Meter View
struct ProfessionalTunerMeterView: View {
    let frequency: Float
    let targetFrequency: Float
    let amplitude: Float
    let tuningAccuracy: TuningAccuracy
    let cents: Float
    
    @State private var needleAngle: Double = 0.0
    
    private let meterSize: CGFloat = 280
    private let needleLength: CGFloat = 120
    private let maxCents: Float = 50.0 // ±50音分的显示范围
    
    var body: some View {
        ZStack {
            // 仪表背景
            MeterBackgroundView(size: meterSize)
            
            // 刻度和标签
            MeterScaleView(size: meterSize, maxCents: maxCents)
            
            // 指针
            MeterNeedleView(
                angle: needleAngle,
                length: needleLength,
                accuracy: tuningAccuracy
            )
            
            // 中心圆点
            Circle()
                .fill(Color.black)
                .frame(width: 12, height: 12)
            
            // 频率显示
            VStack {
                Spacer()
                
                HStack(spacing: 20) {
                    VStack {
                        Text("当前频率")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(String(format: "%.1f Hz", frequency))
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    
                    if targetFrequency > 0 {
                        VStack {
                            Text("目标频率")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text(String(format: "%.1f Hz", targetFrequency))
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                    }
                    
                    VStack {
                        Text("音分差")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(String(format: "%+.0f", cents))
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(centsColor)
                    }
                }
                .padding(.top, 40)
            }
        }
        .frame(width: meterSize, height: meterSize)
        .onChange(of: cents) { _, newCents in
            updateNeedleAngle(cents: newCents)
        }
    }
    
    private var centsColor: Color {
        switch tuningAccuracy {
        case .perfect:
            return .green
        case .tooLow:
            return .blue
        case .tooHigh:
            return .red
        case .outOfRange:
            return .gray
        }
    }
    
    private func updateNeedleAngle(cents: Float) {
        let clampedCents = max(-maxCents, min(maxCents, cents))
        let normalizedCents = clampedCents / maxCents // -1 to 1
        let targetAngle = Double(normalizedCents) * 90.0 // ±90度
        
        withAnimation(.easeInOut(duration: 0.3)) {
            needleAngle = targetAngle
        }
    }
}

// MARK: - Meter Background View
struct MeterBackgroundView: View {
    let size: CGFloat
    
    var body: some View {
        ZStack {
            // 外圆
            Circle()
                .stroke(Color.gray.opacity(0.3), lineWidth: 2)
                .frame(width: size, height: size)
            
            // 内圆背景
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.white,
                            Color.gray.opacity(0.1)
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: size/2
                    )
                )
                .frame(width: size - 20, height: size - 20)
            
            // 中心区域
            Circle()
                .fill(Color.white)
                .frame(width: 80, height: 80)
                .overlay(
                    Circle()
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
        }
    }
}

// MARK: - Meter Scale View
struct MeterScaleView: View {
    let size: CGFloat
    let maxCents: Float
    
    var body: some View {
        ZStack {
            // 主要刻度线 (每10音分)
            ForEach(-5...5, id: \.self) { i in
                let angle = Double(i) * 18.0 // 90度范围内分布
                let cents = Float(i) * 10.0
                
                MeterTickView(
                    angle: angle,
                    length: i == 0 ? 25 : 20,
                    thickness: i == 0 ? 3 : 2,
                    color: i == 0 ? .green : .primary,
                    radius: size/2 - 30
                )
                
                // 刻度标签
                if abs(i) <= 5 {
                    MeterLabelView(
                        text: cents == 0 ? "0" : String(format: "%+.0f", cents),
                        angle: angle,
                        radius: size/2 - 50,
                        color: i == 0 ? .green : .primary
                    )
                }
            }
            
            // 次要刻度线 (每5音分)
            ForEach(-9...9, id: \.self) { i in
                if i % 2 != 0 { // 奇数，即5音分的位置
                    let angle = Double(i) * 9.0
                    MeterTickView(
                        angle: angle,
                        length: 12,
                        thickness: 1,
                        color: .secondary,
                        radius: size/2 - 30
                    )
                }
            }
        }
    }
}

// MARK: - Meter Tick View
struct MeterTickView: View {
    let angle: Double
    let length: CGFloat
    let thickness: CGFloat
    let color: Color
    let radius: CGFloat
    
    var body: some View {
        Rectangle()
            .fill(color)
            .frame(width: thickness, height: length)
            .offset(y: -radius + length/2)
            .rotationEffect(.degrees(angle))
    }
}

// MARK: - Meter Label View
struct MeterLabelView: View {
    let text: String
    let angle: Double
    let radius: CGFloat
    let color: Color
    
    var body: some View {
        Text(text)
            .font(.caption)
            .fontWeight(.medium)
            .foregroundColor(color)
            .offset(y: -radius)
            .rotationEffect(.degrees(angle))
    }
}

// MARK: - Meter Needle View
struct MeterNeedleView: View {
    let angle: Double
    let length: CGFloat
    let accuracy: TuningAccuracy
    
    var body: some View {
        ZStack {
            // 指针主体
            RoundedRectangle(cornerRadius: 2)
                .fill(needleColor)
                .frame(width: 4, height: length)
                .offset(y: -length/2 + 6)
            
            // 指针尖端
            Circle()
                .fill(needleColor)
                .frame(width: 8, height: 8)
                .offset(y: -length + 6)
        }
        .rotationEffect(.degrees(angle))
    }
    
    private var needleColor: Color {
        switch accuracy {
        case .perfect:
            return .green
        case .tooLow:
            return .blue
        case .tooHigh:
            return .red
        case .outOfRange:
            return .gray
        }
    }
}

// MARK: - String Info View
struct StringInfoView: View {
    let string: GuitarString
    let tuning: GuitarTuning
    let accuracy: TuningAccuracy
    
    var body: some View {
        VStack(spacing: 8) {
            Text("检测到的弦")
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack(spacing: 12) {
                Text(string.name)
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("•")
                    .foregroundColor(.secondary)
                
                Text(accuracy.description)
                    .font(.title3)
                    .fontWeight(.medium)
                    .foregroundColor(Color(accuracy.color))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.1))
        )
    }
}

// MARK: - Settings View
struct SettingsView: View {
    @ObservedObject var viewModel: MainViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("设置页面 - 开发中")
                    .font(.title)
                    .padding()
                
                Spacer()
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    ProfessionalTuningView(viewModel: MainViewModel())
}
