import SwiftUI

struct ContentView: View {
    @StateObject private var viewModel = MainViewModel()
    
    var body: some View {
        TabView(selection: $viewModel.selectedTab) {
            QuickTuningView(viewModel: viewModel)
                .tabItem {
                    Image(systemName: Tab.quickTuning.icon)
                    Text(Tab.quickTuning.title)
                }
                .tag(Tab.quickTuning)
            
            Text("专业调音 - 开发中")
                .tabItem {
                    Image(systemName: Tab.professionalTuning.icon)
                    Text(Tab.professionalTuning.title)
                }
                .tag(Tab.professionalTuning)
            
            Text("我的 - 开发中")
                .tabItem {
                    Image(systemName: Tab.profile.icon)
                    Text(Tab.profile.title)
                }
                .tag(Tab.profile)
        }
    }
}

#Preview {
    ContentView()
} 