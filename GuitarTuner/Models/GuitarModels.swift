import Foundation

// MARK: - 吉他弦枚举
enum GuitarString: Int, CaseIterable, Codable {
    case first = 1
    case second = 2
    case third = 3
    case fourth = 4
    case fifth = 5
    case sixth = 6
    
    var name: String {
        switch self {
        case .first: return "第一弦 (E)"
        case .second: return "第二弦 (B)"
        case .third: return "第三弦 (G)"
        case .fourth: return "第四弦 (D)"
        case .fifth: return "第五弦 (A)"
        case .sixth: return "第六弦 (E)"
        }
    }
    
    var note: String {
        switch self {
        case .first: return "E"
        case .second: return "B"
        case .third: return "G"
        case .fourth: return "D"
        case .fifth: return "A"
        case .sixth: return "E"
        }
    }
}

// MARK: - 调音精度
enum TuningAccuracy: Codable {
    case perfect
    case tooLow
    case tooHigh
    case outOfRange
    
    var description: String {
        switch self {
        case .perfect: return "完美"
        case .tooLow: return "偏低"
        case .tooHigh: return "偏高"
        case .outOfRange: return "超出范围"
        }
    }
    
    var color: String {
        switch self {
        case .perfect: return "green"
        case .tooLow: return "blue"
        case .tooHigh: return "red"
        case .outOfRange: return "gray"
        }
    }
}

// MARK: - 调音模式
enum GuitarTuning: String, CaseIterable, Codable {
    case standard = "标准调音"
    case dropD = "Drop D"
    case openG = "Open G"
    case custom = "自定义"
    
    var frequencies: [GuitarString: Float] {
        switch self {
        case .standard:
            return [
                .first: 329.63,   // E4
                .second: 246.94,  // B3
                .third: 196.00,   // G3
                .fourth: 146.83,  // D3
                .fifth: 110.00,   // A2
                .sixth: 82.41     // E2
            ]
        case .dropD:
            return [
                .first: 329.63,   // E4
                .second: 246.94,  // B3
                .third: 196.00,   // G3
                .fourth: 146.83,  // D3
                .fifth: 110.00,   // A2
                .sixth: 73.42     // D2 (降半音)
            ]
        case .openG:
            return [
                .first: 392.00,   // G4
                .second: 246.94,  // B3
                .third: 196.00,   // G3
                .fourth: 146.83,  // D3
                .fifth: 110.00,   // A2
                .sixth: 98.00     // G2
            ]
        case .custom:
            return GuitarTuning.standard.frequencies
        }
    }
}

// MARK: - 用户设置
struct UserSettings: Codable {
    var preferredTuning: GuitarTuning = .standard
    var sensitivity: Float = 10.0 // 音分
    var autoDetect: Bool = true
    var soundEnabled: Bool = true
    var vibrationEnabled: Bool = true
    
    static let `default` = UserSettings()
}

// MARK: - 调音历史
struct TuningHistory: Codable, Identifiable {
    let id: UUID
    let date: Date
    let tuning: GuitarTuning
    let accuracy: [GuitarString: TuningAccuracy]
    let duration: TimeInterval

    init(tuning: GuitarTuning, accuracy: [GuitarString: TuningAccuracy], duration: TimeInterval) {
        self.id = UUID()
        self.date = Date()
        self.tuning = tuning
        self.accuracy = accuracy
        self.duration = duration
    }
}

// MARK: - 音频数据
struct AudioData {
    let frequency: Float
    let amplitude: Float
    let timestamp: Date
    
    init(frequency: Float, amplitude: Float) {
        self.frequency = frequency
        self.amplitude = amplitude
        self.timestamp = Date()
    }
}

// MARK: - 调音结果
struct TuningResult {
    let detectedString: GuitarString?
    let targetFrequency: Float
    let actualFrequency: Float
    let accuracy: TuningAccuracy
    let cents: Float
    
    init(detectedString: GuitarString?, targetFrequency: Float, actualFrequency: Float) {
        self.detectedString = detectedString
        self.targetFrequency = targetFrequency
        self.actualFrequency = actualFrequency
        
        // 计算音分差
        if actualFrequency > 0 && targetFrequency > 0 {
            self.cents = 1200 * log2(actualFrequency / targetFrequency)
        } else {
            self.cents = 0
        }
        
        // 判断精度
        if abs(self.cents) < 10 {
            self.accuracy = .perfect
        } else if self.cents < 0 {
            self.accuracy = .tooLow
        } else {
            self.accuracy = .tooHigh
        }
    }
} 