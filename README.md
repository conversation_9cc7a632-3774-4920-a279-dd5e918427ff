# 吉他调音易 (Guitar Tuner Easy)

一个专业的iOS吉他调音应用，支持电吉他和木吉他的精准调音。

## 📱 应用概述

**吉他调音易**是一款专为吉他手设计的专业调音应用，提供简约易用的界面和高精度的音频识别技术。无论是初学者还是专业音乐人，都能快速、准确地完成吉他调音。

### 🎯 核心特性

- **高精度调音**: 使用先进的FFT算法进行频率分析，确保调音精度
- **智能弦识别**: 自动识别当前弹奏的是第几根弦
- **双模式调音**: 支持快速调音和专业调音两种模式
- **多种调音方式**: 支持标准音和升降音调音（最小半个音）
- **实时降噪**: 内置降噪算法，提高调音准确性
- **离线使用**: 无需网络连接，随时随地调音

## 🎸 支持的调音

### 标准调音 (Standard Tuning)
- 第一弦 (E4): 329.63 Hz
- 第二弦 (B3): 246.94 Hz  
- 第三弦 (G3): 196.00 Hz
- 第四弦 (D3): 146.83 Hz
- 第五弦 (A2): 110.00 Hz
- 第六弦 (E2): 82.41 Hz

### 其他调音方式
- Drop D 调音
- Open G 调音
- 自定义调音

## 🚀 功能介绍

### 1. 快速调音
- **直观界面**: 吉他头形状的六个旋钮显示
- **实时反馈**: 频率仪显示当前音高
- **调音选择**: 支持标准音和升降音切换
- **音准指示**: 清晰显示音准高低状态

### 2. 专业调音
- **专业频率仪**: 精美的频率显示界面
- **实时频谱**: 显示音频频谱和频率变化
- **高精度显示**: 精确到分的音准显示
- **历史记录**: 调音过程的频率变化曲线

### 3. 个人中心
- **用户设置**: 个性化调音配置
- **调音历史**: 记录调音数据和统计
- **偏好设置**: 灵敏度、音效等设置

## 🛠 技术架构

### 开发环境
- **平台**: iOS 14.0+
- **开发语言**: Swift 5.0+
- **UI框架**: SwiftUI
- **架构模式**: MVVM + Combine

### 核心技术
- **音频处理**: AVFoundation + AudioKit
- **频率分析**: FFT算法 + Accelerate Framework
- **数据存储**: Core Data + UserDefaults
- **降噪算法**: 高通滤波器 + 自适应阈值

### 项目结构
```
GuitarTuner/
├── Models/              # 数据模型
│   └── GuitarModels.swift
├── Services/            # 业务服务
│   ├── AudioManager.swift
│   ├── TuningAnalyzer.swift
│   └── SettingsManager.swift
├── ViewModels/          # 视图模型
│   └── MainViewModel.swift
├── Views/               # 用户界面
│   ├── ContentView.swift
│   └── QuickTuningView.swift
└── Assets.xcassets/     # 资源文件
```

## 📋 系统要求

- **设备**: iPhone 6s 及以上
- **系统**: iOS 14.0 或更高版本
- **权限**: 麦克风访问权限
- **存储**: 约 50MB 可用空间

## 🔧 安装与运行

### 开发环境搭建
1. 确保安装了 Xcode 13.0 或更高版本
2. 克隆项目到本地
```bash
git clone [repository-url]
cd guitar_tuner
```

3. 打开项目
```bash
open GuitarTuner.xcodeproj
```

4. 选择目标设备或模拟器
5. 点击运行按钮或使用快捷键 `Cmd + R`

### 依赖管理
项目使用以下主要依赖：
- AudioKit: 专业音频处理
- SwiftLint: 代码规范检查

## 🎵 使用指南

### 首次使用
1. 打开应用后会自动请求麦克风权限
2. 允许权限后即可开始使用
3. 默认进入快速调音界面

### 快速调音
1. 选择调音方式（标准音/升降音）
2. 弹奏需要调音的弦
3. 观察频率仪和吉他头显示
4. 根据指示调整弦的松紧

### 专业调音
1. 切换到专业调音标签
2. 使用精密频率仪进行调音
3. 观察实时频谱变化
4. 达到目标频率即完成调音

## 🔒 隐私与安全

- **本地存储**: 所有数据存储在设备本地
- **无数据上传**: 不会上传任何用户数据
- **权限最小化**: 仅请求必要的麦克风权限
- **数据加密**: 敏感配置使用 Keychain 安全存储

## 🚧 开发计划

### 已完成功能
- [x] 基础项目架构搭建
- [x] 音频采集模块
- [x] 频率分析算法
- [x] 基础UI界面

### 开发中功能
- [ ] 专业调音界面优化
- [ ] 降噪算法完善
- [ ] 用户设置功能

### 计划功能
- [ ] 调音历史记录
- [ ] 更多调音方式支持
- [ ] 界面主题定制
- [ ] 音效反馈

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来帮助改进项目。

### 代码规范
- 遵循 Swift 官方编码规范
- 使用 SwiftLint 进行代码检查
- 提交前确保所有测试通过

### 提交流程
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

**吉他调音易** - 让调音变得简单专业 🎸
