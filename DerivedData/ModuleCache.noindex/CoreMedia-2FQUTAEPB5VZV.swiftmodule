---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/CoreMedia.swiftmodule/arm64-apple-ios-simulator.swiftmodule'
dependencies:
  - mtime:           1742264556000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/CoreMedia.swiftmodule/arm64-apple-ios-simulator.swiftmodule'
    size:            385856
  - mtime:           1741411723000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1929099
    sdk_relative:    true
  - mtime:           1741412425000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1741411513000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1741408955000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1741665624000000000
    path:            'System/Library/Frameworks/CoreAudioTypes.framework/Headers/CoreAudioTypes.apinotes'
    size:            1519
    sdk_relative:    true
  - mtime:           1741412518000000000
    path:            'usr/lib/swift/_errno.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            3851
    sdk_relative:    true
  - mtime:           1741412530000000000
    path:            'usr/lib/swift/_signal.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1062
    sdk_relative:    true
  - mtime:           1741412535000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1063
    sdk_relative:    true
  - mtime:           1741412529000000000
    path:            'usr/lib/swift/_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1026
    sdk_relative:    true
  - mtime:           1741412529000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1474
    sdk_relative:    true
  - mtime:           1741412538000000000
    path:            'usr/lib/swift/unistd.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            814
    sdk_relative:    true
  - mtime:           1741412517000000000
    path:            'usr/lib/swift/_math.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            15245
    sdk_relative:    true
  - mtime:           1741411886000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            4224
    sdk_relative:    true
  - mtime:           1741412547000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            18216
    sdk_relative:    true
  - mtime:           1741412890000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            230593
    sdk_relative:    true
  - mtime:           1741413044000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            22870
    sdk_relative:    true
  - mtime:           1741413566000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            167795
    sdk_relative:    true
  - mtime:           1741413528000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            6558
    sdk_relative:    true
  - mtime:           1741413691000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            57131
    sdk_relative:    true
  - mtime:           1741413840000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            22820
    sdk_relative:    true
  - mtime:           1741411435000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1741409320000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1741748427000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1741413871000000000
    path:            'usr/lib/swift/XPC.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            33270
    sdk_relative:    true
  - mtime:           1741412918000000000
    path:            'usr/lib/swift/Observation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            3451
    sdk_relative:    true
  - mtime:           1741413558000000000
    path:            'usr/lib/swift/System.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            95465
    sdk_relative:    true
  - mtime:           1741753489000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            991696
    sdk_relative:    true
  - mtime:           1741415503000000000
    path:            'usr/lib/swift/CoreAudio.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            9661
    sdk_relative:    true
  - mtime:           1741753036000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            52901
    sdk_relative:    true
  - mtime:           1741837811000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            53005
    sdk_relative:    true
  - mtime:           1741415113000000000
    path:            'System/Library/Frameworks/OpenGLES.framework/Headers/OpenGLES.apinotes'
    size:            1192
    sdk_relative:    true
  - mtime:           1738796627000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            77528
    sdk_relative:    true
  - mtime:           1741410621000000000
    path:            'System/Library/Frameworks/CoreMedia.framework/Headers/CoreMedia.apinotes'
    size:            65704
    sdk_relative:    true
  - mtime:           1741415417000000000
    path:            'usr/lib/swift/Metal.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            25973
    sdk_relative:    true
  - mtime:           1741838451000000000
    path:            'usr/lib/swift/CoreMedia.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            133291
    sdk_relative:    true
version:         1
...
