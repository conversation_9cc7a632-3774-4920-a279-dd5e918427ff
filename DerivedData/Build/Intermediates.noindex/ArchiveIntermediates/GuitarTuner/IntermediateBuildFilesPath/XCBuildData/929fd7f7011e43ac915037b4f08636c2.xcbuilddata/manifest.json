{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath": {"is-mutated": true}, "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation": {"is-mutated": true}, "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app": {"is-mutated": true}, "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner": {"is-mutated": true}, "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath": {"is-mutated": true}, "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>": {"is-command-timestamp": true}, "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>": {"is-command-timestamp": true}, "<TRIGGER: SetOwnerAndGroup jk.blue:staff /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>": {"is-command-timestamp": true}, "<TRIGGER: Strip /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/_CodeSignature", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-scanning>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-end>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-linker-inputs-ready>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-modules-ready>", "<workspace-Release-iphonesimulator18.4-iphonesimulator--stale-file-removal>"], "outputs": ["<all>"]}, "<target-GuitarTuner-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/ssu/root.ssu.yaml", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/_CodeSignature", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM/Contents/Resources/DWARF/GuitarTuner", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Assets.car", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_signature", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Info.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/PkgInfo", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent.der", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent.der", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner Swift Compilation Finished", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_lto.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_dependency_info.dat", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-master.swiftconstvalues", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftmodule", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftsourceinfo", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.abi.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-Swift.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftdoc", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GuitarTuner-Swift.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-all-target-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-generated-files.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-own-target-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-project-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyMetadataFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-OutputFileMap.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.LinkFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftConstValuesFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_const_extract_protocols.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/empty-GuitarTuner.plist"], "roots": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath"], "outputs": ["<target-GuitarTuner-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>"]}, "<workspace-Release-iphonesimulator18.4-iphonesimulator--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner-df1f89dba0f9bac951f1117feebc2816-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Release-iphonesimulator18.4-iphonesimulator--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache", "<ClangStatCache /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk", "-o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner.xcodeproj", "signature": "5fe16cad519a1d5763f32f30d0bb60df"}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator"]}, "P0:::Gate /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM-target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator": {"tool": "phony", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM/Contents/Resources/DWARF/GuitarTuner", "<GenerateDSYMFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM/Contents/Resources/DWARF/GuitarTuner>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM/"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner-df1f89dba0f9bac951f1117feebc2816-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Metadata.appintents>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyMetadataFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftConstValuesFileList"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-ChangePermissions>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-StripSymbols>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<SetMode /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "<SetOwner /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-ChangePermissions>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-ChangeAlternatePermissions>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<CodeSign /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-CodeSign>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-GenerateStubAPI>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-CopyAside>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ProductPostprocessingTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-GenerateStubAPI>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-CodeSign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-Validate>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<Touch /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-RegisterProduct>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-CopyAside>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<Strip /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-StripSymbols>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-Validate": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-RegisterExecutionPolicyException>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<Validate /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-Validate>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-CustomTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-CustomTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-DocumentationTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-GeneratedFilesTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ProductStructureTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent.der", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent.der", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-GeneratedFilesTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-all-target-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-generated-files.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-own-target-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-project-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.hmap"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-HeadermapTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Info.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/PkgInfo", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/empty-GuitarTuner.plist"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-InfoPlistTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleMapTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-RealityAssetsTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleMapTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftPackageCopyFilesTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-InfoPlistTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SanitizerTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftStandardLibrariesTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftFrameworkABICheckerTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftABIBaselineGenerationTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TestTargetTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TestHostTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-CopySwiftPackageResourcesTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TAPISymbolExtractorTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-DocumentationTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-CustomTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-StubBinaryTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-AppIntentsMetadataTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-start>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ProductStructureTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-HeadermapTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-RealityAssetsTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SanitizerTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-StubBinaryTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TestHostTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ProductPostprocessingTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TestTargetTaskProducer>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-copy-headers-completion": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-copy-headers-completion>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Assets.car", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner Swift Compilation Finished", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_lto.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_dependency_info.dat", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-master.swiftconstvalues", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftmodule", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftsourceinfo", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.abi.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-Swift.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftdoc", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-OutputFileMap.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.LinkFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_const_extract_protocols.json"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-generated-headers": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-generated-headers>"]}, "P0:::Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-swift-generated-headers": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-master.swiftconstvalues", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftmodule", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftsourceinfo", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.abi.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-Swift.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftdoc", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GuitarTuner-Swift.h"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-swift-generated-headers>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Metadata.appintents>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyMetadataFileList", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-entry>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Info.plist", "--temp-dir-path", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/ssu", "--bundle-id", "com..GuitarTuner", "--product-path", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "--extracted-metadata-path", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Metadata.appintents", "--deployment-postprocessing", "--metadata-file-list", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyMetadataFileList", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner", "signature": "593549781b576e34e82908860d3f0b76"}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:CodeSign /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Info.plist/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/GuitarTunerApp.swift/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Models/GuitarModels.swift/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Services/AudioManager.swift/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Services/SettingsManager.swift/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Services/TuningAnalyzer.swift/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/ViewModels/MainViewModel.swift/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Views/ContentView.swift/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Views/ProfessionalTuningView.swift/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Views/QuickTuningView.swift/", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-ChangeAlternatePermissions>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-entry>", "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "<TRIGGER: Strip /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/_CodeSignature", "<CodeSign /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "<TRIGGER: CodeSign /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:CompileAssetCatalogVariant thinned /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app /Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app /Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets", "--compile", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "17.6", "--platform", "iphonesimulator"], "env": {}, "working-directory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner", "control-enabled": false, "deps": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "901dc5955593ce4d3fa308f88c79f969"}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:CompileAssetCatalogVariant unthinned /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app /Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app /Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets", "--compile", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "17.6", "--platform", "iphonesimulator"], "env": {}, "working-directory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner", "control-enabled": false, "deps": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "152f3092dfd5bfcebc05fae46c676995"}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:CopySwiftLibs /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"], "deps": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Services/SettingsManager.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Models/GuitarModels.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Services/AudioManager.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Services/TuningAnalyzer.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Views/QuickTuningView.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/ViewModels/MainViewModel.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Views/ContentView.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/GuitarTunerApp.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Views/ProfessionalTuningView.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-master.swiftconstvalues", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyMetadataFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_dependency_info.dat", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftConstValuesFileList", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-entry>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "GuitarTuner", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk", "--xcode-version", "16E140", "--platform-family", "iOS", "--deployment-target", "17.6", "--bundle-identifier", "com..GuitarTuner", "--output", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "--target-triple", "arm64-apple-ios17.6-simulator", "--binary-file", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner", "--dependency-file", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftFileList", "--metadata-file-list", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner", "signature": "84090260b07887fd5c38f3f28f26e9f1"}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-linking": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-linking>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-scanning": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-scanning>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-end": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-entry>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Metadata.appintents>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "<GenerateDSYMFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM/Contents/Resources/DWARF/GuitarTuner>", "<GenerateDSYMFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM/Contents/Resources/DWARF/GuitarTuner>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Assets.car", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "<MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Info.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/PkgInfo", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent.der", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "<SetMode /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "<SetOwner /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "<Strip /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner Swift Compilation Finished", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app", "<Touch /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "<Validate /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_lto.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_dependency_info.dat", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-master.swiftconstvalues", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftmodule", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftsourceinfo", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.abi.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-Swift.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftdoc", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GuitarTuner-Swift.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GuitarTuner-Swift.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-all-target-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-generated-files.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-own-target-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-project-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyMetadataFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-OutputFileMap.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.LinkFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftConstValuesFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_const_extract_protocols.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/empty-GuitarTuner.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM/", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-AppIntentsMetadataTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-ChangeAlternatePermissions>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-ChangePermissions>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-CodeSign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-CopyAside>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-GenerateStubAPI>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-RegisterExecutionPolicyException>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-RegisterProduct>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-StripSymbols>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-Validate>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-CopySwiftPackageResourcesTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-CustomTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-DocumentationTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-GeneratedFilesTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-HeadermapTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-InfoPlistTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleMapTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ProductPostprocessingTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ProductStructureTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-RealityAssetsTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SanitizerTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-StubBinaryTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftABIBaselineGenerationTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftFrameworkABICheckerTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftPackageCopyFilesTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-SwiftStandardLibrariesTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TAPISymbolExtractorTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TestHostTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TestTargetPostprocessingTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-TestTargetTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-copy-headers-completion>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-generated-headers>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-swift-generated-headers>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-end>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-entry": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-entry>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-linker-inputs-ready": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_lto.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_dependency_info.dat", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-master.swiftconstvalues", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftmodule", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftsourceinfo", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.abi.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-Swift.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftdoc", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.LinkFileList"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-linker-inputs-ready>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-modules-ready": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-master.swiftconstvalues", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftmodule", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftsourceinfo", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.abi.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-Swift.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftdoc", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GuitarTuner-Swift.h"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-modules-ready>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-unsigned-product-ready": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/ssu/root.ssu.yaml", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Metadata.appintents>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "<GenerateDSYMFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM/Contents/Resources/DWARF/GuitarTuner>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Assets.car", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent.der", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent.der", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner Swift Compilation Finished", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_lto.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_dependency_info.dat", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-master.swiftconstvalues", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftmodule", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftsourceinfo", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.abi.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-Swift.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftdoc", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GuitarTuner-Swift.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyMetadataFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-OutputFileMap.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.LinkFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftConstValuesFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_const_extract_protocols.json", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-GenerateStubAPI>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-unsigned-product-ready>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Gate target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign": {"tool": "phony", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-unsigned-product-ready>"], "outputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:GenerateAssetSymbols /Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets/", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets", "--compile", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "17.6", "--platform", "iphonesimulator", "--bundle-identifier", "com..GuitarTuner", "--generate-swift-asset-symbols", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner", "control-enabled": false, "signature": "56757ee8456a28ee34674f3a13c66002"}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:GenerateDSYMFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner": {"tool": "shell", "description": "GenerateDSYMFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Info.plist", "<Linked Binary /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftmodule", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM/Contents/Resources/DWARF/GuitarTuner", "<GenerateDSYMFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM/Contents/Resources/DWARF/GuitarTuner>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/dsymutil", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner", "-o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM"], "env": {}, "working-directory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner", "signature": "34a3c9c64e249b524c9f50d1c931a75b"}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:LinkAssetCatalog /Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_signature", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Assets.car"], "deps": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_dependencies"}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-start>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "<MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "<TRIGGER: MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/thinned>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_output/unthinned>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:ProcessInfoPlistFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Info.plist /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/empty-GuitarTuner.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Info.plist /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/empty-GuitarTuner.plist", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/empty-GuitarTuner.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/assetcatalog_generated_info.plist", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-entry>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Info.plist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/PkgInfo"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:ProcessProductPackaging /Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/GuitarTuner.entitlements /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging /Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/GuitarTuner.entitlements /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/GuitarTuner.entitlements", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/Entitlements-Simulated.plist", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ProductStructureTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:ProcessProductPackaging /Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/GuitarTuner.entitlements /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging /Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/GuitarTuner.entitlements /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/GuitarTuner.entitlements", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ProductStructureTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:ProcessProductPackagingDER /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ProductStructureTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent", "-o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner", "signature": "a776de9a514ae67ef3e835700a7ec95a"}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:ProcessProductPackagingDER /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent.der", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ProductStructureTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent", "-o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner", "signature": "789bacbcdb834dd903a30b321a0302a3"}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:RegisterExecutionPolicyException /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-CodeSign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:SetMode u+w,go-w,a+rX /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app": {"tool": "shell", "description": "SetMode u+w,go-w,a+rX /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "inputs": ["<SetOwner /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-StripSymbols>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-entry>", "<TRIGGER: SetOwnerAndGroup jk.blue:staff /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"], "outputs": ["<SetMode /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"], "args": ["/bin/chmod", "-RH", "u+w,go-w,a+rX", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app"], "env": {}, "working-directory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner", "signature": "b894589af5df65e5e538004ab87a7f78"}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:SetOwnerAndGroup jk.blue:staff /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app": {"tool": "shell", "description": "SetOwnerAndGroup jk.blue:staff /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-StripSymbols>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-entry>", "<TRIGGER: MkDir /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"], "outputs": ["<SetOwner /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>", "<TRIGGER: SetOwnerAndGroup jk.blue:staff /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"], "args": ["/usr/sbin/chown", "-RH", "jk.blue:staff", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app"], "env": {}, "working-directory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner", "signature": "d2e71c198e16222b6386295ed279d82a"}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Strip /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner": {"tool": "shell", "description": "Strip /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner", "inputs": ["<GenerateDSYMFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app.dSYM/Contents/Resources/DWARF/GuitarTuner>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-CopyAside>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-entry>", "<TRIGGER: Ld /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner normal>"], "outputs": ["<Strip /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner>", "<TRIGGER: Strip /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip", "-D", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner"], "env": {}, "working-directory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner", "signature": "cff947417fac117f18a7bf7c1f3c780e"}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:SwiftDriver Compilation GuitarTuner normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation GuitarTuner normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Services/SettingsManager.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Models/GuitarModels.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Services/AudioManager.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Services/TuningAnalyzer.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Views/QuickTuningView.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/ViewModels/MainViewModel.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Views/ContentView.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/GuitarTunerApp.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Views/ProfessionalTuningView.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-OutputFileMap.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_const_extract_protocols.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-generated-files.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-own-target-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-all-target-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-project-headers.hmap", "<ClangStatCache /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-copy-headers-completion>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner Swift Compilation Finished"]}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:SymLink /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app ../../InstallationBuildProductsLocation/Applications/GuitarTuner.app": {"tool": "symlink", "description": "SymLink /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app ../../InstallationBuildProductsLocation/Applications/GuitarTuner.app", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-start>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.app"], "contents": "../../InstallationBuildProductsLocation/Applications/GuitarTuner.app", "repair-via-ownership-analysis": true}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Touch /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app": {"tool": "shell", "description": "Touch /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-Validate>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-entry>"], "outputs": ["<Touch /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app"], "env": {}, "working-directory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner", "signature": "717105ffbb2a51ac606d695008696161"}, "P0:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Validate /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/Info.plist", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-Barrier-RegisterExecutionPolicyException>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-will-sign>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-entry>", "<TRIGGER: CodeSign /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"], "outputs": ["<Validate /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app>"]}, "P2:::WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner-df1f89dba0f9bac951f1117feebc2816-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner-df1f89dba0f9bac951f1117feebc2816-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner-df1f89dba0f9bac951f1117feebc2816-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Copy /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.abi.json", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.abi.json/", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-copy-headers-completion>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.abi.json"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Copy /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftdoc", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftdoc/", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-copy-headers-completion>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftdoc"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Copy /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftmodule", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftmodule/", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-copy-headers-completion>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator/GuitarTuner.swiftmodule/arm64-apple-ios-simulator.swiftmodule"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:Ld /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner normal": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner normal", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.LinkFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent.der", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-generated-headers>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-swift-generated-headers>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-linking>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner", "<Linked Binary /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_lto.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios17.6-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk", "-<PERSON><PERSON>", "-L/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "-L/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator", "-F/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "-F/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Release-iphonesimulator", "-filelist", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_lto.o", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-final_output", "-<PERSON><PERSON><PERSON>", "/Applications/GuitarTuner.app/GuitarTuner", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftmodule", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.app-Simulated.xcent.der", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner"], "env": {}, "working-directory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner", "deps": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_dependency_info.dat"], "deps-style": "dependency-info", "signature": "a085c7770bbc1f3212b5bc8b29e7c575"}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:SwiftDriver Compilation Requirements GuitarTuner normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements GuitarTuner normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Services/SettingsManager.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Models/GuitarModels.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Services/AudioManager.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Services/TuningAnalyzer.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Views/QuickTuningView.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/ViewModels/MainViewModel.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Views/ContentView.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/GuitarTunerApp.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner/Views/ProfessionalTuningView.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftFileList", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-OutputFileMap.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_const_extract_protocols.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-generated-files.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-own-target-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-all-target-headers.hmap", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-project-headers.hmap", "<ClangStatCache /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-copy-headers-completion>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-ModuleVerifierTaskProducer>", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/SettingsManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarModels.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/AudioManager.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/TuningAnalyzer.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/QuickTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/MainViewModel.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTunerApp.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/ProfessionalTuningView.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-master.swiftconstvalues", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftmodule", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftsourceinfo", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.abi.json", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-Swift.h", "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.swiftdoc"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:SwiftMergeGeneratedHeaders /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GuitarTuner-Swift.h /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GuitarTuner-Swift.h /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-Swift.h", "inputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-Swift.h", "<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/GuitarTuner-Swift.h"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-all-non-framework-target-headers.hmap", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-all-non-framework-target-headers.hmap"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-all-target-headers.hmap", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-all-target-headers.hmap"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-generated-files.hmap", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-generated-files.hmap"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-own-target-headers.hmap", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-own-target-headers.hmap"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-project-headers.hmap", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner-project-headers.hmap"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyMetadataFileList", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyMetadataFileList"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyStaticMetadataFileList", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.DependencyStaticMetadataFileList"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.hmap", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/GuitarTuner.hmap"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-OutputFileMap.json", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner-OutputFileMap.json"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.LinkFileList", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.LinkFileList"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftConstValuesFileList", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftConstValuesFileList"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftFileList", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner.SwiftFileList"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_const_extract_protocols.json", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/Objects-normal/arm64/GuitarTuner_const_extract_protocols.json"]}, "P2:target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator:Release:WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/empty-GuitarTuner.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/empty-GuitarTuner.plist", "inputs": ["<target-GuitarTuner-****************************************************************-SDKROOT:iphonesimulator:SDK_VARIANT:iphonesimulator-immediate>"], "outputs": ["/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath/GuitarTuner.build/Release-iphonesimulator/GuitarTuner.build/empty-GuitarTuner.plist"]}}}