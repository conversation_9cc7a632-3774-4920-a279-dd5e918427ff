{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "df1f89dba0f9bac951f1117feebc2816a0ecad31cd7996d46f3391ddc4b044e6"}], "containerPath": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "install", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.4", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Products", "derivedDataPath": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Release", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "install", "ASSET_PACK_FOLDER_PATH": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/OnDemandResources", "ASSETCATALOG_COMPILER_FLATTENED_APP_ICON_PATH": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/ProductIcon.png", "COLOR_DIAGNOSTICS": "YES", "DEPLOYMENT_LOCATION": "YES", "DEPLOYMENT_POSTPROCESSING": "YES", "diagnostic_message_length": "244", "DSTROOT": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation", "EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE": "NO", "ENABLE_PREVIEWS": "NO", "ENABLE_SIGNATURE_AGGREGATION": "YES", "ENABLE_XOJIT_PREVIEWS": "YES", "INDEX_ENABLE_DATA_STORE": "NO", "MESSAGES_APPLICATION_EXTENSION_SUPPORT_FOLDER_PATH": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/MessagesApplicationExtensionSupport", "MESSAGES_APPLICATION_SUPPORT_FOLDER_PATH": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/MessagesApplicationSupport", "OBJROOT": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/IntermediateBuildFilesPath", "ONLY_ACTIVE_ARCH": "YES", "SHARED_PRECOMPS_DIR": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/PrecompiledHeaders", "SIGNATURE_METADATA_FOLDER_PATH": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/Signatures", "SWIFT_STDLIB_TOOL_UNSIGNED_DESTINATION_DIR": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/SwiftSupport", "SYMROOT": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath", "WATCHKIT_2_SUPPORT_FOLDER_PATH": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/BuildProductsPath/WatchKitSupport2"}}}}, "schemeCommand": "archive", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}