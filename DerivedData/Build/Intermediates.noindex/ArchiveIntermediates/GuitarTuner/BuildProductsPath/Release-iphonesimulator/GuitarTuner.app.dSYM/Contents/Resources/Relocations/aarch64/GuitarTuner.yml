---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/dev/code/app/guitar_tuner/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/GuitarTuner/InstallationBuildProductsLocation/Applications/GuitarTuner.app/GuitarTuner'
relocations:
  - { offset: 0xA40BF, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerCMU', symObjAddr: 0x670, symBinAddr: 0x100001BB8, symSize: 0x8 }
  - { offset: 0xA40D3, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerCMa', symObjAddr: 0x678, symBinAddr: 0x100001BC0, symSize: 0x38 }
  - { offset: 0xA40E7, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerCMr', symObjAddr: 0x6B0, symBinAddr: 0x100001BF8, symSize: 0xA0 }
  - { offset: 0xA40FB, size: 0x8, addend: 0x0, symName: '_$s7Combine9PublishedVy11GuitarTuner12UserSettingsVGMa', symObjAddr: 0x750, symBinAddr: 0x100001C98, symSize: 0x50 }
  - { offset: 0xA410F, size: 0x8, addend: 0x0, symName: '_$s7Combine9PublishedVySay11GuitarTuner13TuningHistoryVGGMa', symObjAddr: 0x7A0, symBinAddr: 0x100001CE8, symSize: 0x58 }
  - { offset: 0xA4123, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0x7F8, symBinAddr: 0x100001D40, symSize: 0x48 }
  - { offset: 0xA4137, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerC7Combine16ObservableObjectAA0G19WillChangePublisherAdEP_AD0J0PWT', symObjAddr: 0x840, symBinAddr: 0x100001D88, symSize: 0xC }
  - { offset: 0xA414B, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x888, symBinAddr: 0x100001DD0, symSize: 0x44 }
  - { offset: 0xA415F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerC13tuningHistorySayAA06TuningF0VGvpACTK', symObjAddr: 0x8CC, symBinAddr: 0x100001E14, symSize: 0x7C }
  - { offset: 0xA418D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerC13tuningHistorySayAA06TuningF0VGvpACTk', symObjAddr: 0x948, symBinAddr: 0x100001E90, symSize: 0x7C }
  - { offset: 0xA41E0, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0xCC8, symBinAddr: 0x1000021FC, symSize: 0x40 }
  - { offset: 0xA41F4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsVACSeAAWl', symObjAddr: 0xD08, symBinAddr: 0x10000223C, symSize: 0x40 }
  - { offset: 0xA4208, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsVACSEAAWl', symObjAddr: 0xE0C, symBinAddr: 0x100002340, symSize: 0x40 }
  - { offset: 0xA421D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerC8settingsAA04UserC0Vvg', symObjAddr: 0x0, symBinAddr: 0x100001548, symSize: 0x80 }
  - { offset: 0xA4261, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerC13tuningHistorySayAA06TuningF0VGvg', symObjAddr: 0x80, symBinAddr: 0x1000015C8, symSize: 0x70 }
  - { offset: 0xA42C6, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerC7Combine16ObservableObjectAadEP16objectWillChange0giJ9PublisherQzvgTW', symObjAddr: 0x84C, symBinAddr: 0x100001D94, symSize: 0x3C }
  - { offset: 0xA4354, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerCACycfc', symObjAddr: 0xF0, symBinAddr: 0x100001638, symSize: 0x1CC }
  - { offset: 0xA44B0, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerC04saveC033_37C43FA214DD05CFAAD07F0AA81E45D1LLyyF', symObjAddr: 0x2BC, symBinAddr: 0x100001804, symSize: 0x17C }
  - { offset: 0xA452B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerC17saveTuningHistory33_37C43FA214DD05CFAAD07F0AA81E45D1LLyyF', symObjAddr: 0x438, symBinAddr: 0x100001980, symSize: 0x198 }
  - { offset: 0xA45B3, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerCfD', symObjAddr: 0x5D0, symBinAddr: 0x100001B18, symSize: 0xA0 }
  - { offset: 0xA45FB, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerC04loadC033_37C43FA214DD05CFAAD07F0AA81E45D1LLAA04UserC0VyFZTf4d_n', symObjAddr: 0x9D8, symBinAddr: 0x100001F0C, symSize: 0x184 }
  - { offset: 0xA465B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerC17loadTuningHistory33_37C43FA214DD05CFAAD07F0AA81E45D1LLSayAA0fG0VGyFZTf4d_n', symObjAddr: 0xB5C, symBinAddr: 0x100002090, symSize: 0x16C }
  - { offset: 0xA47F1, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x0, symBinAddr: 0x100002380, symSize: 0xC }
  - { offset: 0xA4805, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0xC, symBinAddr: 0x10000238C, symSize: 0x4 }
  - { offset: 0xA4819, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOwup', symObjAddr: 0x18, symBinAddr: 0x100002398, symSize: 0x4 }
  - { offset: 0xA482D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOMa', symObjAddr: 0x1C, symBinAddr: 0x10000239C, symSize: 0x10 }
  - { offset: 0xA4841, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyOMa', symObjAddr: 0x30, symBinAddr: 0x1000023AC, symSize: 0x10 }
  - { offset: 0xA4855, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOwet', symObjAddr: 0x40, symBinAddr: 0x1000023BC, symSize: 0x90 }
  - { offset: 0xA4869, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOwst', symObjAddr: 0xD0, symBinAddr: 0x10000244C, symSize: 0xB0 }
  - { offset: 0xA487D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOwug', symObjAddr: 0x180, symBinAddr: 0x1000024FC, symSize: 0x8 }
  - { offset: 0xA4891, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOwui', symObjAddr: 0x18C, symBinAddr: 0x100002504, symSize: 0x8 }
  - { offset: 0xA48A5, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOMa', symObjAddr: 0x194, symBinAddr: 0x10000250C, symSize: 0x10 }
  - { offset: 0xA48B9, size: 0x8, addend: 0x0, symName: ___swift_memcpy20_4, symObjAddr: 0x1A4, symBinAddr: 0x10000251C, symSize: 0x14 }
  - { offset: 0xA48CD, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12TuningResultVwet', symObjAddr: 0x1B8, symBinAddr: 0x100002530, symSize: 0x44 }
  - { offset: 0xA48E1, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12TuningResultVwst', symObjAddr: 0x1FC, symBinAddr: 0x100002574, symSize: 0x44 }
  - { offset: 0xA48F5, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12TuningResultVMa', symObjAddr: 0x240, symBinAddr: 0x1000025B8, symSize: 0x10 }
  - { offset: 0xA4909, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVwCP', symObjAddr: 0x250, symBinAddr: 0x1000025C8, symSize: 0xC4 }
  - { offset: 0xA491D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVwxx', symObjAddr: 0x314, symBinAddr: 0x10000268C, symSize: 0x6C }
  - { offset: 0xA4931, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVwcp', symObjAddr: 0x380, symBinAddr: 0x1000026F8, symSize: 0x98 }
  - { offset: 0xA4945, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVwca', symObjAddr: 0x418, symBinAddr: 0x100002790, symSize: 0xA8 }
  - { offset: 0xA4959, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVwtk', symObjAddr: 0x4C0, symBinAddr: 0x100002838, symSize: 0x94 }
  - { offset: 0xA496D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVwta', symObjAddr: 0x554, symBinAddr: 0x1000028CC, symSize: 0x9C }
  - { offset: 0xA4981, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVwet', symObjAddr: 0x5F0, symBinAddr: 0x100002968, symSize: 0xC }
  - { offset: 0xA4995, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVwst', symObjAddr: 0x6A4, symBinAddr: 0x100002A1C, symSize: 0xC }
  - { offset: 0xA49A9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVMa', symObjAddr: 0x754, symBinAddr: 0x100002ACC, symSize: 0x38 }
  - { offset: 0xA49BD, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVMr', symObjAddr: 0x78C, symBinAddr: 0x100002B04, symSize: 0xA8 }
  - { offset: 0xA49D1, size: 0x8, addend: 0x0, symName: ___swift_memcpy11_4, symObjAddr: 0x834, symBinAddr: 0x100002BAC, symSize: 0x14 }
  - { offset: 0xA49E5, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsVwet', symObjAddr: 0x848, symBinAddr: 0x100002BC0, symSize: 0x54 }
  - { offset: 0xA49F9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsVwst', symObjAddr: 0x89C, symBinAddr: 0x100002C14, symSize: 0x48 }
  - { offset: 0xA4A0D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsVMa', symObjAddr: 0x8E4, symBinAddr: 0x100002C5C, symSize: 0x10 }
  - { offset: 0xA4F35, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVs12IdentifiableAA2IDsADP_SHWT', symObjAddr: 0x196C, symBinAddr: 0x100003C84, symSize: 0x2C }
  - { offset: 0xA5090, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOs12CaseIterableAA8AllCasessADP_SlWT', symObjAddr: 0x21F4, symBinAddr: 0x100004500, symSize: 0x24 }
  - { offset: 0xA50A4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOSHAASQWb', symObjAddr: 0x2260, symBinAddr: 0x100004524, symSize: 0x4 }
  - { offset: 0xA50B8, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOACSQAAWl', symObjAddr: 0x2264, symBinAddr: 0x100004528, symSize: 0x40 }
  - { offset: 0xA50CC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyOSHAASQWb', symObjAddr: 0x22A4, symBinAddr: 0x100004568, symSize: 0x4 }
  - { offset: 0xA50E0, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyOACSQAAWl', symObjAddr: 0x22A8, symBinAddr: 0x10000456C, symSize: 0x40 }
  - { offset: 0xA50F4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOs12CaseIterableAA8AllCasessADP_SlWT', symObjAddr: 0x22E8, symBinAddr: 0x1000045AC, symSize: 0x24 }
  - { offset: 0xA5108, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOSHAASQWb', symObjAddr: 0x234C, symBinAddr: 0x100004610, symSize: 0x4 }
  - { offset: 0xA511C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOACSQAAWl', symObjAddr: 0x2350, symBinAddr: 0x100004614, symSize: 0x40 }
  - { offset: 0xA51B4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOACSYAAWl', symObjAddr: 0x2AC0, symBinAddr: 0x100004D40, symSize: 0x40 }
  - { offset: 0xA51C8, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_1, symObjAddr: 0x2B00, symBinAddr: 0x100004D80, symSize: 0x24 }
  - { offset: 0xA51DC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs0E3KeyAAWl', symObjAddr: 0x2B24, symBinAddr: 0x100004DA4, symSize: 0x40 }
  - { offset: 0xA51F0, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_1, symObjAddr: 0x2B64, symBinAddr: 0x100004DE4, symSize: 0x20 }
  - { offset: 0xA5204, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO20OutOfRangeCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs0H3KeyAAWl', symObjAddr: 0x2B84, symBinAddr: 0x100004E04, symSize: 0x40 }
  - { offset: 0xA5218, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17TooHighCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs0G3KeyAAWl', symObjAddr: 0x2BC4, symBinAddr: 0x100004E44, symSize: 0x40 }
  - { offset: 0xA522C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO16TooLowCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs0G3KeyAAWl', symObjAddr: 0x2C04, symBinAddr: 0x100004E84, symSize: 0x40 }
  - { offset: 0xA5240, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17PerfectCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs0F3KeyAAWl', symObjAddr: 0x2C44, symBinAddr: 0x100004EC4, symSize: 0x40 }
  - { offset: 0xA5254, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOACSYAAWl', symObjAddr: 0x2C84, symBinAddr: 0x100004F04, symSize: 0x40 }
  - { offset: 0xA5268, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs0E3KeyAAWl', symObjAddr: 0x2CC4, symBinAddr: 0x100004F44, symSize: 0x40 }
  - { offset: 0xA527C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOACSeAAWl', symObjAddr: 0x2D04, symBinAddr: 0x100004F84, symSize: 0x40 }
  - { offset: 0xA5290, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOACSeAAWl', symObjAddr: 0x2D44, symBinAddr: 0x100004FC4, symSize: 0x40 }
  - { offset: 0xA52A4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyOACSeAAWl', symObjAddr: 0x2D84, symBinAddr: 0x100005004, symSize: 0x40 }
  - { offset: 0xA52B8, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVWOc', symObjAddr: 0x2DC4, symBinAddr: 0x100005044, symSize: 0x44 }
  - { offset: 0xA52CC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVWOh', symObjAddr: 0x2E08, symBinAddr: 0x100005088, symSize: 0x3C }
  - { offset: 0xA52E0, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOACSEAAWl', symObjAddr: 0x2E84, symBinAddr: 0x100005104, symSize: 0x40 }
  - { offset: 0xA52F4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOACSEAAWl', symObjAddr: 0x2F40, symBinAddr: 0x1000051C0, symSize: 0x40 }
  - { offset: 0xA5308, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyOACSEAAWl', symObjAddr: 0x2F80, symBinAddr: 0x100005200, symSize: 0x40 }
  - { offset: 0xA531C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs0E3KeyAAWl', symObjAddr: 0x3388, symBinAddr: 0x100005608, symSize: 0x40 }
  - { offset: 0xA5368, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOMa', symObjAddr: 0x360C, symBinAddr: 0x100005888, symSize: 0x10 }
  - { offset: 0xA537C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOMa', symObjAddr: 0x3760, symBinAddr: 0x1000059D8, symSize: 0x10 }
  - { offset: 0xA5390, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOMa', symObjAddr: 0x38B4, symBinAddr: 0x100005B28, symSize: 0x10 }
  - { offset: 0xA53A4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17PerfectCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOMa', symObjAddr: 0x38C4, symBinAddr: 0x100005B38, symSize: 0x10 }
  - { offset: 0xA53B8, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO16TooLowCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOMa', symObjAddr: 0x38D4, symBinAddr: 0x100005B48, symSize: 0x10 }
  - { offset: 0xA53CC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17TooHighCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOMa', symObjAddr: 0x38E4, symBinAddr: 0x100005B58, symSize: 0x10 }
  - { offset: 0xA53E0, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO20OutOfRangeCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOMa', symObjAddr: 0x38F4, symBinAddr: 0x100005B68, symSize: 0x10 }
  - { offset: 0xA53F4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOSHAASQWb', symObjAddr: 0x3904, symBinAddr: 0x100005B78, symSize: 0x4 }
  - { offset: 0xA5408, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFSQAAWl', symObjAddr: 0x3908, symBinAddr: 0x100005B7C, symSize: 0x40 }
  - { offset: 0xA541C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOSHAASQWb', symObjAddr: 0x3948, symBinAddr: 0x100005BBC, symSize: 0x4 }
  - { offset: 0xA5430, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFSQAAWl', symObjAddr: 0x394C, symBinAddr: 0x100005BC0, symSize: 0x40 }
  - { offset: 0xA5444, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOSHAASQWb', symObjAddr: 0x398C, symBinAddr: 0x100005C00, symSize: 0x4 }
  - { offset: 0xA5458, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFSQAAWl', symObjAddr: 0x3990, symBinAddr: 0x100005C04, symSize: 0x40 }
  - { offset: 0xA546C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x39D0, symBinAddr: 0x100005C44, symSize: 0x4 }
  - { offset: 0xA5480, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x39D4, symBinAddr: 0x100005C48, symSize: 0x40 }
  - { offset: 0xA5494, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x3A14, symBinAddr: 0x100005C88, symSize: 0x4 }
  - { offset: 0xA54A8, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs23CustomStringConvertibleAAWl', symObjAddr: 0x3A18, symBinAddr: 0x100005C8C, symSize: 0x40 }
  - { offset: 0xA54BC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x3A58, symBinAddr: 0x100005CCC, symSize: 0x4 }
  - { offset: 0xA54D0, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x3A5C, symBinAddr: 0x100005CD0, symSize: 0x40 }
  - { offset: 0xA54E4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x3A9C, symBinAddr: 0x100005D10, symSize: 0x4 }
  - { offset: 0xA54F8, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs23CustomStringConvertibleAAWl', symObjAddr: 0x3AA0, symBinAddr: 0x100005D14, symSize: 0x40 }
  - { offset: 0xA550C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17PerfectCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0F3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x3AE0, symBinAddr: 0x100005D54, symSize: 0x4 }
  - { offset: 0xA5520, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17PerfectCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x3AE4, symBinAddr: 0x100005D58, symSize: 0x40 }
  - { offset: 0xA5534, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17PerfectCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0F3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x3B24, symBinAddr: 0x100005D98, symSize: 0x4 }
  - { offset: 0xA5548, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17PerfectCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs23CustomStringConvertibleAAWl', symObjAddr: 0x3B28, symBinAddr: 0x100005D9C, symSize: 0x40 }
  - { offset: 0xA555C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO16TooLowCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0G3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x3B68, symBinAddr: 0x100005DDC, symSize: 0x4 }
  - { offset: 0xA5570, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO16TooLowCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x3B6C, symBinAddr: 0x100005DE0, symSize: 0x40 }
  - { offset: 0xA5584, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO16TooLowCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0G3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x3BAC, symBinAddr: 0x100005E20, symSize: 0x4 }
  - { offset: 0xA5598, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO16TooLowCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs23CustomStringConvertibleAAWl', symObjAddr: 0x3BB0, symBinAddr: 0x100005E24, symSize: 0x40 }
  - { offset: 0xA55AC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17TooHighCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0G3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x3BF0, symBinAddr: 0x100005E64, symSize: 0x4 }
  - { offset: 0xA55C0, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17TooHighCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x3BF4, symBinAddr: 0x100005E68, symSize: 0x40 }
  - { offset: 0xA55D4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17TooHighCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0G3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x3C34, symBinAddr: 0x100005EA8, symSize: 0x4 }
  - { offset: 0xA55E8, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17TooHighCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs23CustomStringConvertibleAAWl', symObjAddr: 0x3C38, symBinAddr: 0x100005EAC, symSize: 0x40 }
  - { offset: 0xA55FC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO20OutOfRangeCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0H3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x3C78, symBinAddr: 0x100005EEC, symSize: 0x4 }
  - { offset: 0xA5610, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO20OutOfRangeCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x3C7C, symBinAddr: 0x100005EF0, symSize: 0x40 }
  - { offset: 0xA5624, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO20OutOfRangeCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0H3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x3CBC, symBinAddr: 0x100005F30, symSize: 0x4 }
  - { offset: 0xA5638, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO20OutOfRangeCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs23CustomStringConvertibleAAWl', symObjAddr: 0x3CC0, symBinAddr: 0x100005F34, symSize: 0x40 }
  - { offset: 0xA564C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x3D00, symBinAddr: 0x100005F74, symSize: 0x4 }
  - { offset: 0xA5660, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x3D04, symBinAddr: 0x100005F78, symSize: 0x40 }
  - { offset: 0xA5674, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x3D44, symBinAddr: 0x100005FB8, symSize: 0x4 }
  - { offset: 0xA5688, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOAFs23CustomStringConvertibleAAWl', symObjAddr: 0x3D48, symBinAddr: 0x100005FBC, symSize: 0x40 }
  - { offset: 0xA56BC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x8F4, symBinAddr: 0x100002C6C, symSize: 0x14 }
  - { offset: 0xA570E, size: 0x8, addend: 0x0, symName: '_$ss2eeoiySbx_xtSYRzSQ8RawValueRpzlF11GuitarTuner0D6TuningO_Tg5', symObjAddr: 0x908, symBinAddr: 0x100002C80, symSize: 0x13C }
  - { offset: 0xA5826, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOSHAASH9hashValueSivgTW', symObjAddr: 0xA44, symBinAddr: 0x100002DBC, symSize: 0x44 }
  - { offset: 0xA58E9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xA88, symBinAddr: 0x100002E00, symSize: 0x2C }
  - { offset: 0xA5950, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xAB4, symBinAddr: 0x100002E2C, symSize: 0x40 }
  - { offset: 0xA59FC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0xB64, symBinAddr: 0x100002EDC, symSize: 0x5C }
  - { offset: 0xA5A1F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0xBC0, symBinAddr: 0x100002F38, symSize: 0x4C }
  - { offset: 0xA5A4E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs28CustomDebugStringConvertibleAAsAGP16debugDescriptionSSvgTW', symObjAddr: 0xCC4, symBinAddr: 0x10000303C, symSize: 0x28 }
  - { offset: 0xA5A6A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs23CustomStringConvertibleAAsAGP11descriptionSSvgTW', symObjAddr: 0xCEC, symBinAddr: 0x100003064, symSize: 0x28 }
  - { offset: 0xA5A86, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO20OutOfRangeCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs28CustomDebugStringConvertibleAAsAGP16debugDescriptionSSvgTW', symObjAddr: 0xD2C, symBinAddr: 0x100003098, symSize: 0x28 }
  - { offset: 0xA5AA2, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO20OutOfRangeCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs23CustomStringConvertibleAAsAGP11descriptionSSvgTW', symObjAddr: 0xD54, symBinAddr: 0x1000030C0, symSize: 0x28 }
  - { offset: 0xA5ABE, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17PerfectCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs28CustomDebugStringConvertibleAAsAGP16debugDescriptionSSvgTW', symObjAddr: 0xD94, symBinAddr: 0x1000030E8, symSize: 0x28 }
  - { offset: 0xA5ADA, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17PerfectCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs23CustomStringConvertibleAAsAGP11descriptionSSvgTW', symObjAddr: 0xDBC, symBinAddr: 0x100003110, symSize: 0x28 }
  - { offset: 0xA5AF6, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17TooHighCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs28CustomDebugStringConvertibleAAsAGP16debugDescriptionSSvgTW', symObjAddr: 0xDFC, symBinAddr: 0x100003138, symSize: 0x28 }
  - { offset: 0xA5B12, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO17TooHighCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs23CustomStringConvertibleAAsAGP11descriptionSSvgTW', symObjAddr: 0xE24, symBinAddr: 0x100003160, symSize: 0x28 }
  - { offset: 0xA5B34, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO16TooLowCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs28CustomDebugStringConvertibleAAsAGP16debugDescriptionSSvgTW', symObjAddr: 0xE9C, symBinAddr: 0x1000031C0, symSize: 0x28 }
  - { offset: 0xA5B50, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO16TooLowCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs23CustomStringConvertibleAAsAGP11descriptionSSvgTW', symObjAddr: 0xEC4, symBinAddr: 0x1000031E8, symSize: 0x28 }
  - { offset: 0xA5BB4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x1200, symBinAddr: 0x100003524, symSize: 0xC }
  - { offset: 0xA5C1B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOSHAASH9hashValueSivgTW', symObjAddr: 0x120C, symBinAddr: 0x100003530, symSize: 0xC4 }
  - { offset: 0xA5CC6, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x12D0, symBinAddr: 0x1000035F4, symSize: 0xA0 }
  - { offset: 0xA5D2D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x1370, symBinAddr: 0x100003694, symSize: 0xC0 }
  - { offset: 0xA5DD1, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0x1518, symBinAddr: 0x10000383C, symSize: 0x5C }
  - { offset: 0xA5DF4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0x1574, symBinAddr: 0x100003898, symSize: 0x4C }
  - { offset: 0xA5E52, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs28CustomDebugStringConvertibleAAsAGP16debugDescriptionSSvgTW', symObjAddr: 0x1890, symBinAddr: 0x100003BA8, symSize: 0x28 }
  - { offset: 0xA5E6E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs23CustomStringConvertibleAAsAGP11descriptionSSvgTW', symObjAddr: 0x18B8, symBinAddr: 0x100003BD0, symSize: 0x28 }
  - { offset: 0xA5E90, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs28CustomDebugStringConvertibleAAsAGP16debugDescriptionSSvgTW', symObjAddr: 0x2144, symBinAddr: 0x100004450, symSize: 0x28 }
  - { offset: 0xA5EAC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs23CustomStringConvertibleAAsAGP11descriptionSSvgTW', symObjAddr: 0x216C, symBinAddr: 0x100004478, symSize: 0x28 }
  - { offset: 0xA5EF8, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfC11GuitarTuner0C6StringO_SfTt0g5Tf4g_n', symObjAddr: 0x2390, symBinAddr: 0x100004654, symSize: 0xD4 }
  - { offset: 0xA60D9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOSYAASY8rawValuexSg03RawE0Qz_tcfCTW', symObjAddr: 0xAF4, symBinAddr: 0x100002E6C, symSize: 0x28 }
  - { offset: 0xA6102, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOSYAASY8rawValue03RawE0QzvgTW', symObjAddr: 0xB1C, symBinAddr: 0x100002E94, symSize: 0x10 }
  - { offset: 0xA613F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOs12CaseIterableAAsADP8allCases03AllG0QzvgZTW', symObjAddr: 0xB2C, symBinAddr: 0x100002EA4, symSize: 0x38 }
  - { offset: 0xA61A9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAsAGP11stringValueSSvgTW', symObjAddr: 0xC0C, symBinAddr: 0x100002F84, symSize: 0x7C }
  - { offset: 0xA61E5, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAsAGP11stringValuexSgSS_tcfCTW', symObjAddr: 0xC88, symBinAddr: 0x100003000, symSize: 0x24 }
  - { offset: 0xA620E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAsAGP8intValueSiSgvgTW', symObjAddr: 0xCAC, symBinAddr: 0x100003024, symSize: 0xC }
  - { offset: 0xA6222, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAsAGP8intValuexSgSi_tcfCTW', symObjAddr: 0xCB8, symBinAddr: 0x100003030, symSize: 0xC }
  - { offset: 0xA6236, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO20OutOfRangeCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0H3KeyAAsAGP11stringValueSSvgTW', symObjAddr: 0xD14, symBinAddr: 0x10000308C, symSize: 0xC }
  - { offset: 0xA6250, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO16TooLowCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0G3KeyAAsAGP11stringValuexSgSS_tcfCTW', symObjAddr: 0xE58, symBinAddr: 0x100003188, symSize: 0x2C }
  - { offset: 0xA6264, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO16TooLowCodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0G3KeyAAsAGP8intValuexSgSi_tcfCTW', symObjAddr: 0xE90, symBinAddr: 0x1000031B4, symSize: 0xC }
  - { offset: 0xA6278, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO6encode2toys7Encoder_p_tKF', symObjAddr: 0xEEC, symBinAddr: 0x100003210, symSize: 0x298 }
  - { offset: 0xA62BC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyOSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0x1184, symBinAddr: 0x1000034A8, symSize: 0x28 }
  - { offset: 0xA62E5, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyOSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0x11AC, symBinAddr: 0x1000034D0, symSize: 0x18 }
  - { offset: 0xA62FF, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningO11frequenciesSDyAA0A6StringOSfGvg', symObjAddr: 0x11C4, symBinAddr: 0x1000034E8, symSize: 0x3C }
  - { offset: 0xA635F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOSYAASY8rawValuexSg03RawE0Qz_tcfCTW', symObjAddr: 0x1430, symBinAddr: 0x100003754, symSize: 0x2C }
  - { offset: 0xA6388, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOSYAASY8rawValue03RawE0QzvgTW', symObjAddr: 0x145C, symBinAddr: 0x100003780, symSize: 0x84 }
  - { offset: 0xA63BE, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOs12CaseIterableAAsADP8allCases03AllG0QzvgZTW', symObjAddr: 0x14E0, symBinAddr: 0x100003804, symSize: 0x38 }
  - { offset: 0xA63FC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV6encode2toys7Encoder_p_tKF', symObjAddr: 0x15C0, symBinAddr: 0x1000038E4, symSize: 0x1A4 }
  - { offset: 0xA6455, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x1768, symBinAddr: 0x100003A8C, symSize: 0x28 }
  - { offset: 0xA64C4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAsAGP11stringValueSSvgTW', symObjAddr: 0x1794, symBinAddr: 0x100003AB8, symSize: 0xC0 }
  - { offset: 0xA6515, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAsAGP11stringValuexSgSS_tcfCTW', symObjAddr: 0x1854, symBinAddr: 0x100003B78, symSize: 0x24 }
  - { offset: 0xA653E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAsAGP8intValuexSgSi_tcfCTW', symObjAddr: 0x1884, symBinAddr: 0x100003B9C, symSize: 0xC }
  - { offset: 0xA6565, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsVSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0x18E0, symBinAddr: 0x100003BF8, symSize: 0x44 }
  - { offset: 0xA658E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsVSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0x1924, symBinAddr: 0x100003C3C, symSize: 0x48 }
  - { offset: 0xA65A2, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV6encode2toys7Encoder_p_tKF', symObjAddr: 0x1998, symBinAddr: 0x100003CB0, symSize: 0x254 }
  - { offset: 0xA65D9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV4fromACs7Decoder_p_tKcfC', symObjAddr: 0x1BEC, symBinAddr: 0x100003F04, symSize: 0x420 }
  - { offset: 0xA6613, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAsAGP11stringValueSSvgTW', symObjAddr: 0x2090, symBinAddr: 0x1000043A8, symSize: 0x84 }
  - { offset: 0xA664F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLOs0E3KeyAAsAGP11stringValuexSgSS_tcfCTW', symObjAddr: 0x2114, symBinAddr: 0x10000442C, symSize: 0x24 }
  - { offset: 0xA667E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0x2194, symBinAddr: 0x1000044A0, symSize: 0x14 }
  - { offset: 0xA6692, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0x21A8, symBinAddr: 0x1000044B4, symSize: 0x14 }
  - { offset: 0xA66BE, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryVs12IdentifiableAAsADP2id2IDQzvgTW', symObjAddr: 0x21BC, symBinAddr: 0x1000044C8, symSize: 0x38 }
  - { offset: 0xA66FC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringO8rawValueACSgSi_tcfCTf4nd_n', symObjAddr: 0x2464, symBinAddr: 0x100004728, symSize: 0x28 }
  - { offset: 0xA671F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLO11stringValueAFSgSS_tcfCTf4nd_n', symObjAddr: 0x248C, symBinAddr: 0x100004750, symSize: 0x164 }
  - { offset: 0xA67D6, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAccuracyO4fromACs7Decoder_p_tKcfCTf4nd_n', symObjAddr: 0x25F0, symBinAddr: 0x1000048B4, symSize: 0x430 }
  - { offset: 0xA6872, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningO8rawValueACSgSS_tcfCTf4nd_n', symObjAddr: 0x2A20, symBinAddr: 0x100004CE4, symSize: 0x5C }
  - { offset: 0xA68A9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLO11stringValueAFSgSS_tcfCTf4nd_n', symObjAddr: 0x2FC0, symBinAddr: 0x100005240, symSize: 0x1CC }
  - { offset: 0xA699D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12UserSettingsV4fromACs7Decoder_p_tKcfCTf4nd_n', symObjAddr: 0x318C, symBinAddr: 0x10000540C, symSize: 0x1FC }
  - { offset: 0xA69C0, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13TuningHistoryV10CodingKeys33_2199C04F9E7B9D9CB888C85736A73A36LLO11stringValueAFSgSS_tcfCTf4nd_n', symObjAddr: 0x33C8, symBinAddr: 0x100005648, symSize: 0x1A0 }
  - { offset: 0xA6AA0, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12TuningResultV14detectedString15targetFrequency06actualH0AcA0aF0OSg_S2ftcfCTf4nnnd_n', symObjAddr: 0x3568, symBinAddr: 0x1000057E8, symSize: 0x98 }
  - { offset: 0xA7143, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerCfETo', symObjAddr: 0x5B0, symBinAddr: 0x1000065E0, symSize: 0x128 }
  - { offset: 0xA7172, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerCMU', symObjAddr: 0x6D8, symBinAddr: 0x100006708, symSize: 0x8 }
  - { offset: 0xA7186, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerCMa', symObjAddr: 0x6E0, symBinAddr: 0x100006710, symSize: 0x38 }
  - { offset: 0xA719A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerCMr', symObjAddr: 0x718, symBinAddr: 0x100006748, symSize: 0x104 }
  - { offset: 0xA725E, size: 0x8, addend: 0x0, symName: '_$sSbIegy_SbIeyBy_TR', symObjAddr: 0x1038, symBinAddr: 0x100007068, symSize: 0x3C }
  - { offset: 0xA7297, size: 0x8, addend: 0x0, symName: '_$sSo16AVAudioPCMBufferCSo0A4TimeCIeggg_AbDIeyByy_TR', symObjAddr: 0x2194, symBinAddr: 0x1000081C4, symSize: 0x6C }
  - { offset: 0xA72AF, size: 0x8, addend: 0x0, symName: '_$sSo7NSTimerCIeghg_ABIeyBhy_TR', symObjAddr: 0x2250, symBinAddr: 0x100008280, symSize: 0x50 }
  - { offset: 0xA7406, size: 0x8, addend: 0x0, symName: '_$s7Combine9PublishedVySSSgGMa', symObjAddr: 0x262C, symBinAddr: 0x10000865C, symSize: 0x58 }
  - { offset: 0xA745C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC12errorMessageSSSgvpACTK', symObjAddr: 0x2B2C, symBinAddr: 0x100008B08, symSize: 0x7C }
  - { offset: 0xA748A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC12errorMessageSSSgvpACTk', symObjAddr: 0x2BA8, symBinAddr: 0x100008B84, symSize: 0x7C }
  - { offset: 0xA750F, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSf_Tg5', symObjAddr: 0x2D04, symBinAddr: 0x100008CE0, symSize: 0xF8 }
  - { offset: 0xA7611, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNewAByxGyFSf_Tg5', symObjAddr: 0x2DFC, symBinAddr: 0x100008DD8, symSize: 0x14 }
  - { offset: 0xA768B, size: 0x8, addend: 0x0, symName: '_$ss32_copyCollectionToContiguousArrayys0dE0Vy7ElementQzGxSlRzlFs0E5SliceVySfG_Tg5', symObjAddr: 0x2E10, symBinAddr: 0x100008DEC, symSize: 0xC4 }
  - { offset: 0xA77C5, size: 0x8, addend: 0x0, symName: '_$ss20_ArrayBufferProtocolPsE15replaceSubrange_4with10elementsOfySnySiG_Siqd__ntSlRd__7ElementQyd__AGRtzlFs01_aB0VySfG_s15EmptyCollectionVySfGTg5Tf4nndn_n', symObjAddr: 0x2ED4, symBinAddr: 0x100008EB0, symSize: 0xA4 }
  - { offset: 0xA7890, size: 0x8, addend: 0x0, symName: '_$sSa15replaceSubrange_4withySnySiG_qd__nt7ElementQyd__RszSlRd__lFSf_s15EmptyCollectionVySfGTg5Tf4ndn_n', symObjAddr: 0x2F78, symBinAddr: 0x100008F54, symSize: 0xBC }
  - { offset: 0xA79D7, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0x32F4, symBinAddr: 0x1000092D0, symSize: 0x44 }
  - { offset: 0xA79EB, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC16analyzeFrequency33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFySrySfGzXEfU_yAFzXEfU_yyScMYccfU_TA', symObjAddr: 0x335C, symBinAddr: 0x100009338, symSize: 0xC }
  - { offset: 0xA79FF, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x3368, symBinAddr: 0x100009344, symSize: 0x10 }
  - { offset: 0xA7A13, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x3378, symBinAddr: 0x100009354, symSize: 0x8 }
  - { offset: 0xA7A27, size: 0x8, addend: 0x0, symName: '_$s8Dispatch0A13WorkItemFlagsVACs10SetAlgebraAAWl', symObjAddr: 0x3380, symBinAddr: 0x10000935C, symSize: 0x44 }
  - { offset: 0xA7A3B, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0x3408, symBinAddr: 0x1000093A0, symSize: 0x48 }
  - { offset: 0xA7A4F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC05setupC6Engine33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFyyScMYccfU_TA', symObjAddr: 0x3484, symBinAddr: 0x10000941C, symSize: 0x8 }
  - { offset: 0xA7A63, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC05setupC6Engine33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFyyScMYccfU0_TA', symObjAddr: 0x348C, symBinAddr: 0x100009424, symSize: 0x8 }
  - { offset: 0xA7A77, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC05setupC6Engine33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFySo16AVAudioPCMBufferC_So0O4TimeCtcfU1_TA', symObjAddr: 0x34B8, symBinAddr: 0x100009450, symSize: 0x8 }
  - { offset: 0xA7A8B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC13stopRecordingyyFyyScMYccfU_TA', symObjAddr: 0x3510, symBinAddr: 0x1000094A8, symSize: 0x8 }
  - { offset: 0xA7A9F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC14startRecordingyyFyyScMYccfU0_TA', symObjAddr: 0x3564, symBinAddr: 0x1000094FC, symSize: 0x8 }
  - { offset: 0xA7AB3, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC14startRecordingyyFyyScMYccfU_TA', symObjAddr: 0x356C, symBinAddr: 0x100009504, symSize: 0x8 }
  - { offset: 0xA7AC7, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC18startAnalysisTimer33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFySo7NSTimerCYbcfU_TA', symObjAddr: 0x3574, symBinAddr: 0x10000950C, symSize: 0x8 }
  - { offset: 0xA7ADB, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC05setupC7Session33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFyyScMYccfU_TA', symObjAddr: 0x35A8, symBinAddr: 0x100009540, symSize: 0x8 }
  - { offset: 0xA7AEF, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC27requestMicrophonePermission33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFySbcfU_TA', symObjAddr: 0x35B0, symBinAddr: 0x100009548, symSize: 0x8 }
  - { offset: 0xA7B03, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC27requestMicrophonePermission33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFySbcfU_yyScMYccfU_TA', symObjAddr: 0x35DC, symBinAddr: 0x100009574, symSize: 0xC }
  - { offset: 0xA7D8E, size: 0x8, addend: 0x0, symName: '_$sSaySayxGqd__c7ElementQyd__RszSTRd__lufCSf_s10ArraySliceVySfGTt0g5', symObjAddr: 0x26D8, symBinAddr: 0x1000086B4, symSize: 0xCC }
  - { offset: 0xA7F68, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC12errorMessageSSSgvg', symObjAddr: 0xA4, symBinAddr: 0x1000060D4, symSize: 0x70 }
  - { offset: 0xA7FE7, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC7Combine16ObservableObjectAadEP16objectWillChange0giJ9PublisherQzvgTW', symObjAddr: 0x2AF0, symBinAddr: 0x100008ACC, symSize: 0x3C }
  - { offset: 0xA804D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerCACycfc', symObjAddr: 0x190, symBinAddr: 0x1000061C0, symSize: 0x358 }
  - { offset: 0xA816C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerCACycfcTo', symObjAddr: 0x4E8, symBinAddr: 0x100006518, symSize: 0x20 }
  - { offset: 0xA8180, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerCfD', symObjAddr: 0x508, symBinAddr: 0x100006538, symSize: 0x4C }
  - { offset: 0xA81B1, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerCfDTo', symObjAddr: 0x554, symBinAddr: 0x100006584, symSize: 0x5C }
  - { offset: 0xA829C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC8setupFFT33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyF', symObjAddr: 0x81C, symBinAddr: 0x10000684C, symSize: 0x14C }
  - { offset: 0xA864E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC05setupC7Session33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyF', symObjAddr: 0x968, symBinAddr: 0x100006998, symSize: 0x33C }
  - { offset: 0xA8721, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC05setupC7Session33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFyyScMYccfU_', symObjAddr: 0xCA4, symBinAddr: 0x100006CD4, symSize: 0xE0 }
  - { offset: 0xA8852, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC27requestMicrophonePermission33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFySbcfU_', symObjAddr: 0xD84, symBinAddr: 0x100006DB4, symSize: 0x1B4 }
  - { offset: 0xA88C1, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC27requestMicrophonePermission33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFySbcfU_yyScMYccfU_', symObjAddr: 0xF38, symBinAddr: 0x100006F68, symSize: 0x100 }
  - { offset: 0xA89E8, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC14startRecordingyyF', symObjAddr: 0x1074, symBinAddr: 0x1000070A4, symSize: 0x558 }
  - { offset: 0xA8B48, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC14startRecordingyyFyyScMYccfU_', symObjAddr: 0x15CC, symBinAddr: 0x1000075FC, symSize: 0xAC }
  - { offset: 0xA8BC3, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC14startRecordingyyFyyScMYccfU0_', symObjAddr: 0x1678, symBinAddr: 0x1000076A8, symSize: 0xE0 }
  - { offset: 0xA8CFA, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC13stopRecordingyyF', symObjAddr: 0x1758, symBinAddr: 0x100007788, symSize: 0x3C8 }
  - { offset: 0xA8EF9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC13stopRecordingyyFyyScMYccfU_', symObjAddr: 0x1B20, symBinAddr: 0x100007B50, symSize: 0xE8 }
  - { offset: 0xA904C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC05setupC6Engine33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyF', symObjAddr: 0x1C08, symBinAddr: 0x100007C38, symSize: 0x434 }
  - { offset: 0xA91CC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC05setupC6Engine33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFyyScMYccfU_', symObjAddr: 0x203C, symBinAddr: 0x10000806C, symSize: 0x80 }
  - { offset: 0xA9234, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC05setupC6Engine33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFyyScMYccfU0_', symObjAddr: 0x20BC, symBinAddr: 0x1000080EC, symSize: 0x80 }
  - { offset: 0xA929C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC05setupC6Engine33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFySo16AVAudioPCMBufferC_So0O4TimeCtcfU1_', symObjAddr: 0x213C, symBinAddr: 0x10000816C, symSize: 0x58 }
  - { offset: 0xA92E4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC18startAnalysisTimer33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFySo7NSTimerCYbcfU_', symObjAddr: 0x2200, symBinAddr: 0x100008230, symSize: 0x50 }
  - { offset: 0xA933D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC07processC6Buffer33_C385A9EE879EB19BAE8CFE1573E98DD2LLyySo16AVAudioPCMBufferCF', symObjAddr: 0x22A0, symBinAddr: 0x1000082D0, symSize: 0x1C8 }
  - { offset: 0xA9806, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC16analyzeFrequency33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyF', symObjAddr: 0x2468, symBinAddr: 0x100008498, symSize: 0x180 }
  - { offset: 0xA9BB1, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC16analyzeFrequency33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFySrySfGzXEfU_yAFzXEfU_', symObjAddr: 0x27A4, symBinAddr: 0x100008780, symSize: 0x294 }
  - { offset: 0xA9D64, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC16analyzeFrequency33_C385A9EE879EB19BAE8CFE1573E98DD2LLyyFySrySfGzXEfU_yAFzXEfU_yyScMYccfU_', symObjAddr: 0x2A38, symBinAddr: 0x100008A14, symSize: 0xB8 }
  - { offset: 0xA9E5D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC20interpolateFrequency33_C385A9EE879EB19BAE8CFE1573E98DD2LL3bin10magnitudes14freqResolutionSfSi_SaySfGSftFTf4nnnd_n', symObjAddr: 0x3034, symBinAddr: 0x100009010, symSize: 0xBC }
  - { offset: 0xAA00D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC17findPeakFrequency33_C385A9EE879EB19BAE8CFE1573E98DD2LL10magnitudesSf9frequency_Sf9amplitudetSaySfG_tFTf4nd_n', symObjAddr: 0x30F0, symBinAddr: 0x1000090CC, symSize: 0x9C }
  - { offset: 0xAA031, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC17findPeakFrequency33_C385A9EE879EB19BAE8CFE1573E98DD2LL10magnitudesSf9frequency_Sf9amplitudetSaySfG_tFTf4nd_n', symObjAddr: 0x30F0, symBinAddr: 0x1000090CC, symSize: 0x9C }
  - { offset: 0xAA050, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC17findPeakFrequency33_C385A9EE879EB19BAE8CFE1573E98DD2LL10magnitudesSf9frequency_Sf9amplitudetSaySfG_tFTf4nd_n', symObjAddr: 0x30F0, symBinAddr: 0x1000090CC, symSize: 0x9C }
  - { offset: 0xAA06F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC17findPeakFrequency33_C385A9EE879EB19BAE8CFE1573E98DD2LL10magnitudesSf9frequency_Sf9amplitudetSaySfG_tFTf4nd_n', symObjAddr: 0x30F0, symBinAddr: 0x1000090CC, symSize: 0x9C }
  - { offset: 0xAA08E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC17findPeakFrequency33_C385A9EE879EB19BAE8CFE1573E98DD2LL10magnitudesSf9frequency_Sf9amplitudetSaySfG_tFTf4nd_n', symObjAddr: 0x30F0, symBinAddr: 0x1000090CC, symSize: 0x9C }
  - { offset: 0xAA0A9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC17findPeakFrequency33_C385A9EE879EB19BAE8CFE1573E98DD2LL10magnitudesSf9frequency_Sf9amplitudetSaySfG_tFTf4nd_n', symObjAddr: 0x30F0, symBinAddr: 0x1000090CC, symSize: 0x9C }
  - { offset: 0xAA0C8, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC17findPeakFrequency33_C385A9EE879EB19BAE8CFE1573E98DD2LL10magnitudesSf9frequency_Sf9amplitudetSaySfG_tFTf4nd_n', symObjAddr: 0x30F0, symBinAddr: 0x1000090CC, symSize: 0x9C }
  - { offset: 0xAA0DD, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC17findPeakFrequency33_C385A9EE879EB19BAE8CFE1573E98DD2LL10magnitudesSf9frequency_Sf9amplitudetSaySfG_tFTf4nd_n', symObjAddr: 0x30F0, symBinAddr: 0x1000090CC, symSize: 0x9C }
  - { offset: 0xAA0F1, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC17findPeakFrequency33_C385A9EE879EB19BAE8CFE1573E98DD2LL10magnitudesSf9frequency_Sf9amplitudetSaySfG_tFTf4nd_n', symObjAddr: 0x30F0, symBinAddr: 0x1000090CC, symSize: 0x9C }
  - { offset: 0xAA289, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12AudioManagerC06detectA6String9frequency6tuningAA0aF0OSgSf_AA0A6TuningOtFTf4nnd_n', symObjAddr: 0x318C, symBinAddr: 0x100009168, symSize: 0x168 }
  - { offset: 0xAA59D, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x0, symBinAddr: 0x1000095E0, symSize: 0x2C }
  - { offset: 0xAA808, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerCMU', symObjAddr: 0xD9C, symBinAddr: 0x10000A37C, symSize: 0x8 }
  - { offset: 0xAA81C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerCMa', symObjAddr: 0xDA4, symBinAddr: 0x10000A384, symSize: 0x38 }
  - { offset: 0xAA830, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerCMr', symObjAddr: 0xDDC, symBinAddr: 0x10000A3BC, symSize: 0x120 }
  - { offset: 0xAA844, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC13currentResultAA0cF0VSgvpACTK', symObjAddr: 0x10DC, symBinAddr: 0x10000A624, symSize: 0x84 }
  - { offset: 0xAA872, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC13currentResultAA0cF0VSgvpACTk', symObjAddr: 0x1160, symBinAddr: 0x10000A6A8, symSize: 0x80 }
  - { offset: 0xAA8B2, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC14detectedStringAA0aF0OSgvpACTK', symObjAddr: 0x11E0, symBinAddr: 0x10000A728, symSize: 0x7C }
  - { offset: 0xAA8E0, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC14detectedStringAA0aF0OSgvpACTk', symObjAddr: 0x125C, symBinAddr: 0x10000A7A4, symSize: 0x70 }
  - { offset: 0xAA91E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC14tuningAccuracyAA0cF0OvpACTK', symObjAddr: 0x12CC, symBinAddr: 0x10000A814, symSize: 0x7C }
  - { offset: 0xAA94C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC14tuningAccuracyAA0cF0OvpACTk', symObjAddr: 0x1348, symBinAddr: 0x10000A890, symSize: 0x70 }
  - { offset: 0xAA98A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC5centsSfvpACTk', symObjAddr: 0x1444, symBinAddr: 0x10000A98C, symSize: 0x78 }
  - { offset: 0xAA9D4, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlF11GuitarTuner0J6StringO_Tg5', symObjAddr: 0x14BC, symBinAddr: 0x10000AA04, symSize: 0x5C }
  - { offset: 0xAAA92, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlF11GuitarTuner0L6StringO_Tg5', symObjAddr: 0x1518, symBinAddr: 0x10000AA60, symSize: 0xA0 }
  - { offset: 0xAAB59, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC11resetResult33_413C47A191BDBF771B0A29A3CC3F0EE7LLyyFyyScMYccfU_TA', symObjAddr: 0x1864, symBinAddr: 0x10000AD24, symSize: 0x8 }
  - { offset: 0xAAB6D, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x186C, symBinAddr: 0x10000AD2C, symSize: 0x10 }
  - { offset: 0xAAB81, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x187C, symBinAddr: 0x10000AD3C, symSize: 0x8 }
  - { offset: 0xAAB95, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC16analyzeFrequency9frequency9amplitudeySf_SftFyyScMYccfU_TA', symObjAddr: 0x192C, symBinAddr: 0x10000ADA8, symSize: 0x10 }
  - { offset: 0xAABA9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC13setupBindings33_413C47A191BDBF771B0A29A3CC3F0EE7LLyyFySf_SftcfU_TA', symObjAddr: 0x19F0, symBinAddr: 0x10000AE6C, symSize: 0x8 }
  - { offset: 0xAABC8, size: 0x8, addend: 0x0, symName: '_$sS2fIegyy_Sf_SftIegn_TRTA', symObjAddr: 0x1A1C, symBinAddr: 0x10000AE98, symSize: 0x24 }
  - { offset: 0xAACC0, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC13currentResultAA0cF0VSgvg', symObjAddr: 0x2C, symBinAddr: 0x10000960C, symSize: 0x74 }
  - { offset: 0xAACDF, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC14detectedStringAA0aF0OSgvg', symObjAddr: 0xA0, symBinAddr: 0x100009680, symSize: 0x70 }
  - { offset: 0xAACFE, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC14tuningAccuracyAA0cF0Ovg', symObjAddr: 0x110, symBinAddr: 0x1000096F0, symSize: 0x70 }
  - { offset: 0xAAD1D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC5centsSfvg', symObjAddr: 0x180, symBinAddr: 0x100009760, symSize: 0x70 }
  - { offset: 0xAADBC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC7Combine16ObservableObjectAadEP16objectWillChange0giJ9PublisherQzvgTW', symObjAddr: 0xFE0, symBinAddr: 0x10000A528, symSize: 0x3C }
  - { offset: 0xAAE2A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC13setupBindings33_413C47A191BDBF771B0A29A3CC3F0EE7LLyyF', symObjAddr: 0x1F0, symBinAddr: 0x1000097D0, symSize: 0x27C }
  - { offset: 0xAAE7E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC13setupBindings33_413C47A191BDBF771B0A29A3CC3F0EE7LLyyFySf_SftcfU_', symObjAddr: 0x46C, symBinAddr: 0x100009A4C, symSize: 0x6C }
  - { offset: 0xAAF07, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC03setC0yyAA0aC0OF', symObjAddr: 0x4D8, symBinAddr: 0x100009AB8, symSize: 0xFC }
  - { offset: 0xAAFCB, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC16analyzeFrequency9frequency9amplitudeySf_SftF', symObjAddr: 0x5D4, symBinAddr: 0x100009BB4, symSize: 0x290 }
  - { offset: 0xAB0E9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC16analyzeFrequency9frequency9amplitudeySf_SftFyyScMYccfU_', symObjAddr: 0x864, symBinAddr: 0x100009E44, symSize: 0x15C }
  - { offset: 0xAB247, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC11resetResult33_413C47A191BDBF771B0A29A3CC3F0EE7LLyyF', symObjAddr: 0x9C0, symBinAddr: 0x100009FA0, symSize: 0x1B4 }
  - { offset: 0xAB295, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC11resetResult33_413C47A191BDBF771B0A29A3CC3F0EE7LLyyFyyScMYccfU_', symObjAddr: 0xB74, symBinAddr: 0x10000A154, symSize: 0x140 }
  - { offset: 0xAB379, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerCfD', symObjAddr: 0xCB4, symBinAddr: 0x10000A294, symSize: 0xE8 }
  - { offset: 0xAB42A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14TuningAnalyzerC12audioManagerAcA05AudioF0C_tcfcTf4gn_n', symObjAddr: 0x15B8, symBinAddr: 0x10000AB00, symSize: 0x224 }
  - { offset: 0xAB673, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewVwCP', symObjAddr: 0x0, symBinAddr: 0x10000AF04, symSize: 0x2C }
  - { offset: 0xAB687, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewVwxx', symObjAddr: 0x2C, symBinAddr: 0x10000AF30, symSize: 0x28 }
  - { offset: 0xAB69B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewVwcp', symObjAddr: 0x54, symBinAddr: 0x10000AF58, symSize: 0x44 }
  - { offset: 0xAB6AF, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewVwca', symObjAddr: 0x98, symBinAddr: 0x10000AF9C, symSize: 0x6C }
  - { offset: 0xAB6C3, size: 0x8, addend: 0x0, symName: ___swift_memcpy32_8, symObjAddr: 0x104, symBinAddr: 0x10000B008, symSize: 0xC }
  - { offset: 0xAB6D7, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewVwta', symObjAddr: 0x110, symBinAddr: 0x10000B014, symSize: 0x4C }
  - { offset: 0xAB6EB, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewVwet', symObjAddr: 0x15C, symBinAddr: 0x10000B060, symSize: 0x48 }
  - { offset: 0xAB6FF, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewVwst', symObjAddr: 0x1A4, symBinAddr: 0x10000B0A8, symSize: 0x40 }
  - { offset: 0xAB713, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewVMa', symObjAddr: 0x1E4, symBinAddr: 0x10000B0E8, symSize: 0x10 }
  - { offset: 0xAB727, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewV7SwiftUI0E0AA4BodyAdEP_AGWT', symObjAddr: 0x1F4, symBinAddr: 0x10000B0F8, symSize: 0x10 }
  - { offset: 0xAB974, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVy11GuitarTuner016FrequencyDisplayD0V_AD012TuningResultD0VAD0e4HeadD0VAD014ControlButtonsD0VtGWOr', symObjAddr: 0xB08, symBinAddr: 0x10000BA0C, symSize: 0x34 }
  - { offset: 0xAB988, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVy11GuitarTuner016FrequencyDisplayD0V_AD012TuningResultD0VAD0e4HeadD0VAD014ControlButtonsD0VtGWOs', symObjAddr: 0xB3C, symBinAddr: 0x10000BA40, symSize: 0x34 }
  - { offset: 0xAB99C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewV4bodyQrvgAA0d8SelectorE0VycfU0_TA', symObjAddr: 0xBB8, symBinAddr: 0x10000BA78, symSize: 0x10 }
  - { offset: 0xAB9B0, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewVMa', symObjAddr: 0xBC8, symBinAddr: 0x10000BA88, symSize: 0x38 }
  - { offset: 0xAB9C4, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6VStackVyAA9TupleViewVy11GuitarTuner016FrequencyDisplayG0V_AH012TuningResultG0VAH0h4HeadG0VAH014ControlButtonsG0VtGGAA14_PaddingLayoutVGACyxq_GAA0G0A2aWRzAA0G8ModifierR_rlWl', symObjAddr: 0xC00, symBinAddr: 0x10000BAC0, symSize: 0x88 }
  - { offset: 0xAB9D8, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6VStackVyAA9TupleViewVy11GuitarTuner016FrequencyDisplayG0V_AH012TuningResultG0VAH0h4HeadG0VAH014ControlButtonsG0VtGGAA14_PaddingLayoutVGWOs', symObjAddr: 0xCD0, symBinAddr: 0x10000BB48, symSize: 0x34 }
  - { offset: 0xAB9EC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewV4bodyQrvgAA0d8SelectorE0VycfU0_AA0aD0OyYbcfU_TA', symObjAddr: 0xD48, symBinAddr: 0x10000BB80, symSize: 0x14 }
  - { offset: 0xABA00, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewV4bodyQrvgAA0d8SelectorE0VycfU0_yAA0aD0OYbcfU0_TA', symObjAddr: 0xD90, symBinAddr: 0x10000BBC8, symSize: 0x34 }
  - { offset: 0xABF4B, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI17EnvironmentValuesV7dismissAA13DismissActionVvpACTK', symObjAddr: 0x4720, symBinAddr: 0x10000F558, symSize: 0x20 }
  - { offset: 0xABF6A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewV4bodyQrvg7SwiftUI05TupleE0VyAA016FrequencyDisplayE0V_AA0d6ResultE0VAA0a4HeadE0VAA014ControlButtonsE0VtGyXEfU_yycAA04MainE5ModelCcfu_yycfu0_TA', symObjAddr: 0x48FC, symBinAddr: 0x10000F734, symSize: 0x8 }
  - { offset: 0xABF7E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewV4bodyQrvg7SwiftUI05TupleE0VyAA016FrequencyDisplayE0V_AA0d6ResultE0VAA0a4HeadE0VAA014ControlButtonsE0VtGyXEfU_yycfU_TA', symObjAddr: 0x4930, symBinAddr: 0x10000F768, symSize: 0x54 }
  - { offset: 0xABFB2, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewVwCP', symObjAddr: 0x4984, symBinAddr: 0x10000F7BC, symSize: 0xE8 }
  - { offset: 0xABFC6, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewVwxx', symObjAddr: 0x4A6C, symBinAddr: 0x10000F8A4, symSize: 0x7C }
  - { offset: 0xABFDA, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewVwcp', symObjAddr: 0x4AE8, symBinAddr: 0x10000F920, symSize: 0xB4 }
  - { offset: 0xABFEE, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewVwca', symObjAddr: 0x4B9C, symBinAddr: 0x10000F9D4, symSize: 0xEC }
  - { offset: 0xAC002, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewVwtk', symObjAddr: 0x4C88, symBinAddr: 0x10000FAC0, symSize: 0xAC }
  - { offset: 0xAC016, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewVwta', symObjAddr: 0x4D34, symBinAddr: 0x10000FB6C, symSize: 0xE0 }
  - { offset: 0xAC02A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewVwet', symObjAddr: 0x4E14, symBinAddr: 0x10000FC4C, symSize: 0xC }
  - { offset: 0xAC03E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewVwst', symObjAddr: 0x4EA0, symBinAddr: 0x10000FCD8, symSize: 0xC }
  - { offset: 0xAC052, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewVMr', symObjAddr: 0x4F24, symBinAddr: 0x10000FD5C, symSize: 0x74 }
  - { offset: 0xAC066, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI11EnvironmentV7ContentOyAA13DismissActionV_GMa', symObjAddr: 0x4F98, symBinAddr: 0x10000FDD0, symSize: 0x54 }
  - { offset: 0xAC07A, size: 0x8, addend: 0x0, symName: ___swift_memcpy3_1, symObjAddr: 0x4FEC, symBinAddr: 0x10000FE24, symSize: 0x14 }
  - { offset: 0xAC08E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A8HeadViewVwet', symObjAddr: 0x5004, symBinAddr: 0x10000FE38, symSize: 0x70 }
  - { offset: 0xAC0A2, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A8HeadViewVwst', symObjAddr: 0x5074, symBinAddr: 0x10000FEA8, symSize: 0x8C }
  - { offset: 0xAC0B6, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A8HeadViewVMa', symObjAddr: 0x5100, symBinAddr: 0x10000FF34, symSize: 0x10 }
  - { offset: 0xAC0CA, size: 0x8, addend: 0x0, symName: ___swift_memcpy28_4, symObjAddr: 0x5110, symBinAddr: 0x10000FF44, symSize: 0x14 }
  - { offset: 0xAC0DE, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner16TuningResultViewVwet', symObjAddr: 0x5124, symBinAddr: 0x10000FF58, symSize: 0x44 }
  - { offset: 0xAC0F2, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner16TuningResultViewVwst', symObjAddr: 0x5168, symBinAddr: 0x10000FF9C, symSize: 0x48 }
  - { offset: 0xAC106, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner16TuningResultViewVMa', symObjAddr: 0x51B0, symBinAddr: 0x10000FFE4, symSize: 0x10 }
  - { offset: 0xAC11A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewVwxx', symObjAddr: 0x51C0, symBinAddr: 0x10000FFF4, symSize: 0x28 }
  - { offset: 0xAC12E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewVwcp', symObjAddr: 0x51E8, symBinAddr: 0x10001001C, symSize: 0x50 }
  - { offset: 0xAC142, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewVwca', symObjAddr: 0x5238, symBinAddr: 0x10001006C, symSize: 0x64 }
  - { offset: 0xAC156, size: 0x8, addend: 0x0, symName: ___swift_memcpy40_8, symObjAddr: 0x529C, symBinAddr: 0x1000100D0, symSize: 0x14 }
  - { offset: 0xAC16A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewVwta', symObjAddr: 0x52B0, symBinAddr: 0x1000100E4, symSize: 0x4C }
  - { offset: 0xAC17E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewVwet', symObjAddr: 0x52FC, symBinAddr: 0x100010130, symSize: 0x48 }
  - { offset: 0xAC192, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewVwst', symObjAddr: 0x5344, symBinAddr: 0x100010178, symSize: 0x48 }
  - { offset: 0xAC1A6, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewVMa', symObjAddr: 0x538C, symBinAddr: 0x1000101C0, symSize: 0x10 }
  - { offset: 0xAC1BA, size: 0x8, addend: 0x0, symName: ___swift_memcpy8_4, symObjAddr: 0x539C, symBinAddr: 0x1000101D0, symSize: 0xC }
  - { offset: 0xAC1CE, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner20FrequencyDisplayViewVwet', symObjAddr: 0x53A8, symBinAddr: 0x1000101DC, symSize: 0x20 }
  - { offset: 0xAC1E2, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner20FrequencyDisplayViewVwst', symObjAddr: 0x53C8, symBinAddr: 0x1000101FC, symSize: 0x28 }
  - { offset: 0xAC1F6, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner20FrequencyDisplayViewVMa', symObjAddr: 0x53F0, symBinAddr: 0x100010224, symSize: 0x10 }
  - { offset: 0xAC20A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner20FrequencyDisplayViewV7SwiftUI0E0AA4BodyAdEP_AGWT', symObjAddr: 0x5488, symBinAddr: 0x1000102BC, symSize: 0x10 }
  - { offset: 0xAC21E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewV7SwiftUI0E0AA4BodyAdEP_AGWT', symObjAddr: 0x5498, symBinAddr: 0x1000102CC, symSize: 0x10 }
  - { offset: 0xAC232, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner16TuningResultViewV7SwiftUI0E0AA4BodyAdEP_AGWT', symObjAddr: 0x54A8, symBinAddr: 0x1000102DC, symSize: 0x10 }
  - { offset: 0xAC246, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A8HeadViewV7SwiftUI0D0AA4BodyAdEP_AGWT', symObjAddr: 0x54B8, symBinAddr: 0x1000102EC, symSize: 0x10 }
  - { offset: 0xAC25A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV7SwiftUI0E0AA4BodyAdEP_AGWT', symObjAddr: 0x54C8, symBinAddr: 0x1000102FC, symSize: 0x10 }
  - { offset: 0xAC26E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_TA', symObjAddr: 0x54D8, symBinAddr: 0x10001030C, symSize: 0x8 }
  - { offset: 0xAC282, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewVWOb', symObjAddr: 0x54E4, symBinAddr: 0x100010318, symSize: 0x44 }
  - { offset: 0xAC296, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_A20_AZcfU_TA', symObjAddr: 0x5528, symBinAddr: 0x10001035C, symSize: 0x4C }
  - { offset: 0xAC2AA, size: 0x8, addend: 0x0, symName: '_$s2os32getNullTerminatedUTF8PointerImpl_21storingStringOwnersInSVSS_SpyypGSgztF', symObjAddr: 0x5758, symBinAddr: 0x10001058C, symSize: 0xC8 }
  - { offset: 0xAC2CD, size: 0x8, addend: 0x0, symName: '_$ss11_StringGutsV16_deconstructUTF87scratchyXlSg5owner_xSi6lengthSb11usesScratchSb15allocatedMemorytSwSg_ts8_PointerRzlFSV_Tgq5', symObjAddr: 0x5820, symBinAddr: 0x100010654, symSize: 0x108 }
  - { offset: 0xAC345, size: 0x8, addend: 0x0, symName: '_$ss11_StringGutsV23_allocateForDeconstructyXl5owner_SVSi6lengthtyF', symObjAddr: 0x5928, symBinAddr: 0x10001075C, symSize: 0x98 }
  - { offset: 0xAC3B4, size: 0x8, addend: 0x0, symName: '_$ss32_copyCollectionToContiguousArrayys0dE0Vy7ElementQzGxSlRzlFSS8UTF8ViewV_Tgq5', symObjAddr: 0x59C0, symBinAddr: 0x1000107F4, symSize: 0x98 }
  - { offset: 0xAC3F5, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCs5UInt8V_Tt1gq5', symObjAddr: 0x5A58, symBinAddr: 0x10001088C, symSize: 0x68 }
  - { offset: 0xAC44A, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFs5UInt8V_Tgq5', symObjAddr: 0x5AC0, symBinAddr: 0x1000108F4, symSize: 0xE8 }
  - { offset: 0xAC4DC, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ListVAAs5NeverORszrlE_2id10rowContentACyAeA7ForEachVyqd__qd_0_qd_1_GGqd___s7KeyPathCy7ElementQyd__qd_0_Gqd_1_AOctcAJRs_SkRd__SHRd_0_AA4ViewRd_1_r1_lufcAJyXEfU_Say11GuitarTuner0N6TuningOG_AtA6ButtonVyAA6HStackVyAA05TupleM0VyAA4TextV_AA6SpacerVAA08ModifiedG0VyAA5ImageVAA012_EnvironmentJ15WritingModifierVyAA5ColorVSgGGSgtGGGTG5', symObjAddr: 0x5BA8, symBinAddr: 0x1000109DC, symSize: 0x118 }
  - { offset: 0xAC501, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ListVAAs5NeverORszrlE_2id10rowContentACyAeA7ForEachVyqd__qd_0_qd_1_GGqd___s7KeyPathCy7ElementQyd__qd_0_Gqd_1_AOctcAJRs_SkRd__SHRd_0_AA4ViewRd_1_r1_lufcAJyXEfU_Say11GuitarTuner0N6TuningOG_AtA6ButtonVyAA6HStackVyAA05TupleM0VyAA4TextV_AA6SpacerVAA08ModifiedG0VyAA5ImageVAA012_EnvironmentJ15WritingModifierVyAA5ColorVSgGGSgtGGGTG5TA', symObjAddr: 0x5CC0, symBinAddr: 0x100010AF4, symSize: 0xC }
  - { offset: 0xAC515, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI7ForEachVySay11GuitarTuner0E6TuningOGAfA6ButtonVyAA6HStackVyAA9TupleViewVyAA4TextV_AA6SpacerVAA15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGSgtGGGGACyxq_q0_GAA0K0A2AA7_R0_rlWl', symObjAddr: 0x5CCC, symBinAddr: 0x100010B00, symSize: 0x80 }
  - { offset: 0xAC529, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_A32_yXEfU0_TA', symObjAddr: 0x5D4C, symBinAddr: 0x100010B80, symSize: 0x8 }
  - { offset: 0xAC53D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_A32_yXEfU0_A30_yXEfU_TA', symObjAddr: 0x5D54, symBinAddr: 0x100010B88, symSize: 0x8 }
  - { offset: 0xAC551, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_A32_yXEfU0_A30_yXEfU_yyScMYccfU_TA', symObjAddr: 0x5E10, symBinAddr: 0x100010C44, symSize: 0x2C }
  - { offset: 0xAC565, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x5E5C, symBinAddr: 0x100010C70, symSize: 0x3C }
  - { offset: 0xAC58F, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ListVAAs5NeverORszrlE_2id10rowContentACyAeA7ForEachVyqd__qd_0_qd_1_GGqd___s7KeyPathCy7ElementQyd__qd_0_Gqd_1_AOctcAJRs_SkRd__SHRd_0_AA4ViewRd_1_r1_lufcAJyXEfU_qd_1_AOcfU_Say11GuitarTuner0N6TuningOG_AtA6ButtonVyAA6HStackVyAA05TupleM0VyAA4TextV_AA6SpacerVAA08ModifiedG0VyAA5ImageVAA012_EnvironmentJ15WritingModifierVyAA5ColorVSgGGSgtGGGTG5TA', symObjAddr: 0x5EBC, symBinAddr: 0x100010CD0, symSize: 0x34 }
  - { offset: 0xAC5CD, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6TuningOACSHAAWl', symObjAddr: 0x5EF0, symBinAddr: 0x100010D04, symSize: 0x40 }
  - { offset: 0xAC5E1, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_A20_AZcfU_yyScMYccfU_TA', symObjAddr: 0x5FE8, symBinAddr: 0x100010DFC, symSize: 0x34 }
  - { offset: 0xAC5F5, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_A20_AZcfU_A19_yXEfU0_TA', symObjAddr: 0x601C, symBinAddr: 0x100010E30, symSize: 0xC }
  - { offset: 0xAC609, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA4TextV_AA6SpacerVAA15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGSgtGWOr', symObjAddr: 0x6028, symBinAddr: 0x100010E3C, symSize: 0x64 }
  - { offset: 0xAC61D, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4TextV7StorageOWOy', symObjAddr: 0x608C, symBinAddr: 0x100010EA0, symSize: 0x10 }
  - { offset: 0xAC631, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGSgWOy', symObjAddr: 0x609C, symBinAddr: 0x100010EB0, symSize: 0x3C }
  - { offset: 0xAC645, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA4TextV_AA6SpacerVAA15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGSgtGWOs', symObjAddr: 0x60D8, symBinAddr: 0x100010EEC, symSize: 0x64 }
  - { offset: 0xAC659, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4TextV7StorageOWOe', symObjAddr: 0x613C, symBinAddr: 0x100010F50, symSize: 0x10 }
  - { offset: 0xAC66D, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGSgWOe', symObjAddr: 0x614C, symBinAddr: 0x100010F60, symSize: 0x38 }
  - { offset: 0xAC681, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x6184, symBinAddr: 0x100010F98, symSize: 0x40 }
  - { offset: 0xAC695, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI16RoundedRectangleVWOh', symObjAddr: 0x6210, symBinAddr: 0x100011024, symSize: 0x3C }
  - { offset: 0xAC6A9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A8HeadViewV4bodyQrvg7SwiftUI05TupleD0VyAE4TextV_AE6HStackVyAE7ForEachVySayAA0A6StringOGAoA0amD0VGGtGyXEfU_ASyXEfU_ArOcfU_TA', symObjAddr: 0x625C, symBinAddr: 0x100011070, symSize: 0x10 }
  - { offset: 0xAC6BD, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A6StringOACSHAAWl', symObjAddr: 0x626C, symBinAddr: 0x100011080, symSize: 0x40 }
  - { offset: 0xAC6D1, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A10StringViewVAC7SwiftUI0D0AAWl', symObjAddr: 0x62AC, symBinAddr: 0x1000110C0, symSize: 0x40 }
  - { offset: 0xAC6E5, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI19_ConditionalContentV7StorageOyAA9TupleViewVyAA4TextV_A2ItGAI_GWOy', symObjAddr: 0x62EC, symBinAddr: 0x100011100, symSize: 0xA8 }
  - { offset: 0xAC6F9, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI19_ConditionalContentV7StorageOyAA9TupleViewVyAA4TextV_A2ItGAI_GWOe', symObjAddr: 0x63F0, symBinAddr: 0x100011204, symSize: 0xA8 }
  - { offset: 0xAC70D, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA6VStackVyAA012_ConditionalD0VyAA9TupleViewVyAA4TextV_A2KtGAKGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGWOr', symObjAddr: 0x6498, symBinAddr: 0x1000112AC, symSize: 0x64 }
  - { offset: 0xAC721, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA6VStackVyAA012_ConditionalD0VyAA9TupleViewVyAA4TextV_A2KtGAKGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGWOs', symObjAddr: 0x64FC, symBinAddr: 0x100011310, symSize: 0x64 }
  - { offset: 0xAC735, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyACyAA6HStackVyAA9TupleViewVyAA5ImageV_AA4TextVtGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGAA022_EnvironmentKeyWritingN0VyATSgGGAA11_ClipEffectVyAA16RoundedRectangleVGGACyxq_GAA0G0A2AA7_RzAA0gN0R_rlWl', symObjAddr: 0x6560, symBinAddr: 0x100011374, symSize: 0x88 }
  - { offset: 0xAC749, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyAA6HStackVyAA9TupleViewVyAA5ImageV_AA4TextVtGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGAA022_EnvironmentKeyWritingN0VyATSgGGACyxq_GAA0G0A2AA1_RzAA0gN0R_rlWl', symObjAddr: 0x65E8, symBinAddr: 0x1000113FC, symSize: 0x88 }
  - { offset: 0xAC75D, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA6HStackVyAA9TupleViewVyAA5ImageV_AA4TextVtGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGACyxq_GAA0G0A2aXRzAA0gN0R_rlWl', symObjAddr: 0x6670, symBinAddr: 0x100011484, symSize: 0x88 }
  - { offset: 0xAC771, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6HStackVyAA9TupleViewVyAA5ImageV_AA4TextVtGGAA14_PaddingLayoutVGACyxq_GAA0G0A2aRRzAA0G8ModifierR_rlWl', symObjAddr: 0x66F8, symBinAddr: 0x10001150C, symSize: 0x88 }
  - { offset: 0xAC785, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewV4bodyQrvg7SwiftUI05TupleE0VyAE6ButtonVyAE0E0PAEE12cornerRadius_11antialiasedQr12CoreGraphics7CGFloatV_SbtFQOyAkEE15foregroundColoryQrAE0R0VSgFQOyAkEE10background_20ignoresSafeAreaEdgesQrqd___AE4EdgeO3SetVtAE10ShapeStyleRd__lFQOyAkEE7paddingyQrAZ_APSgtFQOyAE6HStackVyAGyAE5ImageV_AE4TextVtGG_Qo__ASQo__Qo__Qo_G_A14_tGyXEfU_A13_yXEfU0_TA', symObjAddr: 0x6780, symBinAddr: 0x100011594, symSize: 0x8 }
  - { offset: 0xAC799, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyAA6HStackVyAA9TupleViewVyAA5ImageV_AA4TextVtGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGAA022_EnvironmentKeyWritingN0VyATSgGGWOr', symObjAddr: 0x6788, symBinAddr: 0x10001159C, symSize: 0x84 }
  - { offset: 0xAC7AD, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyAA6HStackVyAA9TupleViewVyAA5ImageV_AA4TextVtGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGAA022_EnvironmentKeyWritingN0VyATSgGGWOs', symObjAddr: 0x680C, symBinAddr: 0x100011620, symSize: 0x84 }
  - { offset: 0xAC7C1, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA4TextV_AE11GuitarTuner18AmplitudeIndicatorVtGWOr', symObjAddr: 0x6890, symBinAddr: 0x1000116A4, symSize: 0x70 }
  - { offset: 0xAC7D5, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA4TextV_AE11GuitarTuner18AmplitudeIndicatorVtGWOs', symObjAddr: 0x6900, symBinAddr: 0x100011714, symSize: 0x70 }
  - { offset: 0xAC7E9, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA6VStackVyAA9TupleViewVyAA4TextV_AI11GuitarTuner18AmplitudeIndicatorVtGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGWOr', symObjAddr: 0x6970, symBinAddr: 0x100011784, symSize: 0x84 }
  - { offset: 0xAC7FD, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA6VStackVyAA9TupleViewVyAA4TextV_AI11GuitarTuner18AmplitudeIndicatorVtGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGWOs', symObjAddr: 0x69F4, symBinAddr: 0x100011808, symSize: 0x84 }
  - { offset: 0xAC811, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A10StringViewVwet', symObjAddr: 0x6A78, symBinAddr: 0x10001188C, symSize: 0x54 }
  - { offset: 0xAC825, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A10StringViewVwst', symObjAddr: 0x6ACC, symBinAddr: 0x1000118E0, symSize: 0x40 }
  - { offset: 0xAC839, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A10StringViewVMa', symObjAddr: 0x6B0C, symBinAddr: 0x100011920, symSize: 0x10 }
  - { offset: 0xAC84D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18AmplitudeIndicatorVMa', symObjAddr: 0x6B1C, symBinAddr: 0x100011930, symSize: 0x10 }
  - { offset: 0xAC861, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyAA6VStackVyAA9TupleViewVyAA4TextV_AA6HStackVyAA7ForEachVySay11GuitarTuner0L6StringOGApN0lnG0VGGtGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGAA11_ClipEffectVyAA16RoundedRectangleVGGACyxq_GAA0G0A2AA12_RzAA0gS0R_rlWl', symObjAddr: 0x6B5C, symBinAddr: 0x100011970, symSize: 0x88 }
  - { offset: 0xAC875, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA6VStackVyAA9TupleViewVyAA4TextV_AA6HStackVyAA7ForEachVySay11GuitarTuner0L6StringOGApN0lnG0VGGtGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGACyxq_GAA0G0A2AA6_RzAA0gS0R_rlWl', symObjAddr: 0x6BE4, symBinAddr: 0x1000119F8, symSize: 0x88 }
  - { offset: 0xAC889, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6VStackVyAA9TupleViewVyAA4TextV_AA6HStackVyAA7ForEachVySay11GuitarTuner0L6StringOGApN0lnG0VGGtGGAA14_PaddingLayoutVGACyxq_GAA0G0A2AA0_RzAA0G8ModifierR_rlWl', symObjAddr: 0x6C6C, symBinAddr: 0x100011A80, symSize: 0x88 }
  - { offset: 0xAC89D, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyAA6VStackVyAA012_ConditionalD0VyAA9TupleViewVyAA4TextV_A2KtGAKGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGAA11_ClipEffectVyAA16RoundedRectangleVGGACyxq_GAA0H0A2AA3_RzAA0hN0R_rlWl', symObjAddr: 0x6CF8, symBinAddr: 0x100011B0C, symSize: 0x88 }
  - { offset: 0xAC8B1, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA6VStackVyAA012_ConditionalD0VyAA9TupleViewVyAA4TextV_A2KtGAKGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGACyxq_GAA0H0A2aYRzAA0hN0R_rlWl', symObjAddr: 0x6D80, symBinAddr: 0x100011B94, symSize: 0x88 }
  - { offset: 0xAC8C5, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6VStackVyAA012_ConditionalD0VyAA9TupleViewVyAA4TextV_A2KtGAKGGAA14_PaddingLayoutVGACyxq_GAA0H0A2aSRzAA0H8ModifierR_rlWl', symObjAddr: 0x6E08, symBinAddr: 0x100011C1C, symSize: 0x88 }
  - { offset: 0xAC8D9, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyAA6VStackVyAA9TupleViewVyAA4TextV_AI11GuitarTuner18AmplitudeIndicatorVtGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGAA11_ClipEffectVyAA16RoundedRectangleVGGACyxq_GAA0G0A2AA3_RzAA0gQ0R_rlWl', symObjAddr: 0x6EC0, symBinAddr: 0x100011CD4, symSize: 0x88 }
  - { offset: 0xAC8ED, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA6VStackVyAA9TupleViewVyAA4TextV_AI11GuitarTuner18AmplitudeIndicatorVtGGAA14_PaddingLayoutVGAA24_BackgroundStyleModifierVyAA5ColorVGGACyxq_GAA0G0A2aYRzAA0gQ0R_rlWl', symObjAddr: 0x6F48, symBinAddr: 0x100011D5C, symSize: 0x88 }
  - { offset: 0xAC901, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6VStackVyAA9TupleViewVyAA4TextV_AI11GuitarTuner18AmplitudeIndicatorVtGGAA14_PaddingLayoutVGACyxq_GAA0G0A2aSRzAA0G8ModifierR_rlWl', symObjAddr: 0x6FD0, symBinAddr: 0x100011DE4, symSize: 0x88 }
  - { offset: 0xAC915, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18AmplitudeIndicatorV7SwiftUI4ViewAA4BodyAdEP_AGWT', symObjAddr: 0x7058, symBinAddr: 0x100011E6C, symSize: 0x10 }
  - { offset: 0xAC929, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A10StringViewV7SwiftUI0D0AA4BodyAdEP_AGWT', symObjAddr: 0x7068, symBinAddr: 0x100011E7C, symSize: 0x10 }
  - { offset: 0xAC93D, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA15ModifiedContentVyAEyAEyAEyAA06_ShapeD0VyAA6CircleVAA5ColorVGAA12_FrameLayoutVGAA16_OverlayModifierVyAA4TextVGGAA12_ScaleEffectVGAA010_AnimationM0VySbGG_A2StGWOr', symObjAddr: 0x7078, symBinAddr: 0x100011E8C, symSize: 0xD0 }
  - { offset: 0xAC951, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA15ModifiedContentVyAEyAEyAEyAA06_ShapeD0VyAA6CircleVAA5ColorVGAA12_FrameLayoutVGAA16_OverlayModifierVyAA4TextVGGAA12_ScaleEffectVGAA010_AnimationM0VySbGG_A2StGWOs', symObjAddr: 0x7148, symBinAddr: 0x100011F5C, symSize: 0xD0 }
  - { offset: 0xAC965, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyACyAA10_ShapeViewVyAA6CircleVAA5ColorVGAA12_FrameLayoutVGAA16_OverlayModifierVyAA4TextVGGAA12_ScaleEffectVGAA010_AnimationL0VySbGGWOr', symObjAddr: 0x7218, symBinAddr: 0x10001202C, symSize: 0x68 }
  - { offset: 0xAC979, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyACyAA10_ShapeViewVyAA6CircleVAA5ColorVGAA12_FrameLayoutVGAA16_OverlayModifierVyAA4TextVGGAA12_ScaleEffectVGAA010_AnimationL0VySbGGWOs', symObjAddr: 0x7280, symBinAddr: 0x100012094, symSize: 0x68 }
  - { offset: 0xAC98D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18AmplitudeIndicatorV4bodyQrvg7SwiftUI7ForEachVySnySiGSiAE4ViewPAEE12cornerRadius_11antialiasedQr12CoreGraphics7CGFloatV_SbtFQOyAjEE5frame5width6height9alignmentQrAOSg_AtE9AlignmentVtFQOyAE06_ShapeJ0VyAE9RectangleVAE5ColorVG_Qo__Qo_GyXEfU_A3_SicfU_TA', symObjAddr: 0x72F8, symBinAddr: 0x10001210C, symSize: 0x8 }
  - { offset: 0xAC9A1, size: 0x8, addend: 0x0, symName: '_$sSnySiGSnyxGSksSxRzSZ6StrideRpzrlWl', symObjAddr: 0x7300, symBinAddr: 0x100012114, symSize: 0x70 }
  - { offset: 0xAC9B5, size: 0x8, addend: 0x0, symName: '_$sS2iSZsWl', symObjAddr: 0x7370, symBinAddr: 0x100012184, symSize: 0x40 }
  - { offset: 0xAC9C9, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA10_ShapeViewVyAA9RectangleVAA5ColorVGAA12_FrameLayoutVGAA11_ClipEffectVyAA07RoundedG0VGGACyxq_GAA0F0A2aURzAA0F8ModifierR_rlWl', symObjAddr: 0x73B0, symBinAddr: 0x1000121C4, symSize: 0x88 }
  - { offset: 0xAC9DD, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA10_ShapeViewVyAA9RectangleVAA5ColorVGAA12_FrameLayoutVGACyxq_GAA0F0A2aORzAA0F8ModifierR_rlWl', symObjAddr: 0x7438, symBinAddr: 0x10001224C, symSize: 0x88 }
  - { offset: 0xAC9F1, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA10_ShapeViewVyAA9RectangleVAA5ColorVGAA12_FrameLayoutVGWOr', symObjAddr: 0x7540, symBinAddr: 0x100012354, symSize: 0x28 }
  - { offset: 0xACA05, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA10_ShapeViewVyAA9RectangleVAA5ColorVGAA12_FrameLayoutVGWOs', symObjAddr: 0x7568, symBinAddr: 0x10001237C, symSize: 0x28 }
  - { offset: 0xACA19, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6HStackVyAA7ForEachVySnySiGSiACyACyAA10_ShapeViewVyAA9RectangleVAA5ColorVGAA12_FrameLayoutVGAA11_ClipEffectVyAA07RoundedJ0VGGGGAQGACyxq_GAA0I0A2AA1_RzAA0I8ModifierR_rlWl', symObjAddr: 0x7604, symBinAddr: 0x100012418, symSize: 0x88 }
  - { offset: 0xAD2C4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A10StringViewV7SwiftUI0D0AadEP05_makeD04view6inputsAD01_D7OutputsVAD11_GraphValueVyxG_AD01_D6InputsVtFZTW', symObjAddr: 0x312C, symBinAddr: 0x10000DF64, symSize: 0x4 }
  - { offset: 0xAD2E0, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A10StringViewV7SwiftUI0D0AadEP05_makeD4List4view6inputsAD01_dH7OutputsVAD11_GraphValueVyxG_AD01_dH6InputsVtFZTW', symObjAddr: 0x3130, symBinAddr: 0x10000DF68, symSize: 0x4 }
  - { offset: 0xAD2FC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A10StringViewV7SwiftUI0D0AadEP14_viewListCount6inputsSiSgAD01_dhI6InputsV_tFZTW', symObjAddr: 0x3134, symBinAddr: 0x10000DF6C, symSize: 0x18 }
  - { offset: 0xAD775, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewV4bodyQrvg', symObjAddr: 0x204, symBinAddr: 0x10000B108, symSize: 0x25C }
  - { offset: 0xAD83E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewV4bodyQrvg7SwiftUI05TupleE0VyAA016FrequencyDisplayE0V_AA0d6ResultE0VAA0a4HeadE0VAA014ControlButtonsE0VtGyXEfU_', symObjAddr: 0x460, symBinAddr: 0x10000B364, symSize: 0x408 }
  - { offset: 0xAD990, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewV4bodyQrvg7SwiftUI05TupleE0VyAA016FrequencyDisplayE0V_AA0d6ResultE0VAA0a4HeadE0VAA014ControlButtonsE0VtGyXEfU_yycAA04MainE5ModelCcfu_yycfu0_', symObjAddr: 0x868, symBinAddr: 0x10000B76C, symSize: 0x98 }
  - { offset: 0xADA0F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewV4bodyQrvgAA0d8SelectorE0VycfU0_', symObjAddr: 0x900, symBinAddr: 0x10000B804, symSize: 0x170 }
  - { offset: 0xADA5D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewV4bodyQrvgAA0d8SelectorE0VycfU0_AA0aD0OyYbcfU_', symObjAddr: 0xA70, symBinAddr: 0x10000B974, symSize: 0x88 }
  - { offset: 0xADBF1, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewV7SwiftUI0E0AadEP4body4BodyQzvgTW', symObjAddr: 0xAF8, symBinAddr: 0x10000B9FC, symSize: 0x10 }
  - { offset: 0xADC30, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner20FrequencyDisplayViewV4bodyQrvg', symObjAddr: 0xDC4, symBinAddr: 0x10000BBFC, symSize: 0x298 }
  - { offset: 0xADD5E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner20FrequencyDisplayViewV4bodyQrvg7SwiftUI05TupleE0VyAE4TextV_AiA18AmplitudeIndicatorVtGyXEfU_', symObjAddr: 0x105C, symBinAddr: 0x10000BE94, symSize: 0x3C0 }
  - { offset: 0xADE29, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner20FrequencyDisplayViewV7SwiftUI0E0AadEP4body4BodyQzvgTW', symObjAddr: 0x141C, symBinAddr: 0x10000C254, symSize: 0x8 }
  - { offset: 0xADE57, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18AmplitudeIndicatorV4bodyQrvg', symObjAddr: 0x1424, symBinAddr: 0x10000C25C, symSize: 0x19C }
  - { offset: 0xADF4C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18AmplitudeIndicatorV4bodyQrvg7SwiftUI7ForEachVySnySiGSiAE4ViewPAEE12cornerRadius_11antialiasedQr12CoreGraphics7CGFloatV_SbtFQOyAjEE5frame5width6height9alignmentQrAOSg_AtE9AlignmentVtFQOyAE06_ShapeJ0VyAE9RectangleVAE5ColorVG_Qo__Qo_GyXEfU_A3_SicfU_', symObjAddr: 0x15C0, symBinAddr: 0x10000C3F8, symSize: 0x27C }
  - { offset: 0xAE136, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18AmplitudeIndicatorV7SwiftUI4ViewAadEP4body4BodyQzvgTW', symObjAddr: 0x183C, symBinAddr: 0x10000C674, symSize: 0x8 }
  - { offset: 0xAE16B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner16TuningResultViewV4bodyQrvg', symObjAddr: 0x1844, symBinAddr: 0x10000C67C, symSize: 0x2CC }
  - { offset: 0xAE299, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner16TuningResultViewV4bodyQrvg7SwiftUI19_ConditionalContentVyAE05TupleE0VyAE4TextV_A2KtGAKGyXEfU_', symObjAddr: 0x1B10, symBinAddr: 0x10000C948, symSize: 0x8A0 }
  - { offset: 0xAE475, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner16TuningResultViewV7SwiftUI0E0AadEP4body4BodyQzvgTW', symObjAddr: 0x23B0, symBinAddr: 0x10000D1E8, symSize: 0x18 }
  - { offset: 0xAE4A3, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A8HeadViewV4bodyQrvg', symObjAddr: 0x23C8, symBinAddr: 0x10000D200, symSize: 0x264 }
  - { offset: 0xAE635, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A8HeadViewV4bodyQrvg7SwiftUI05TupleD0VyAE4TextV_AE6HStackVyAE7ForEachVySayAA0A6StringOGAoA0amD0VGGtGyXEfU_', symObjAddr: 0x262C, symBinAddr: 0x10000D464, symSize: 0x308 }
  - { offset: 0xAE6E8, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A8HeadViewV4bodyQrvg7SwiftUI05TupleD0VyAE4TextV_AE6HStackVyAE7ForEachVySayAA0A6StringOGAoA0amD0VGGtGyXEfU_ASyXEfU_ArOcfU_', symObjAddr: 0x2934, symBinAddr: 0x10000D76C, symSize: 0x98 }
  - { offset: 0xAE78A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A8HeadViewV7SwiftUI0D0AadEP4body4BodyQzvgTW', symObjAddr: 0x29CC, symBinAddr: 0x10000D804, symSize: 0x10 }
  - { offset: 0xAE7EA, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A10StringViewV4bodyQrvg7SwiftUI05TupleD0VyAE0D0PAEE9animation_5valueQrAE9AnimationVSg_qd__tSQRd__lFQOyAiEE11scaleEffect_6anchorQr12CoreGraphics7CGFloatV_AE9UnitPointVtFQOyAiEE7overlay_9alignmentQrqd___AE9AlignmentVtAeHRd__lFQOyAiEE5frame5width6heightAWQrASSg_A1_AYtFQOyAE06_ShapeD0VyAE6CircleVAE5ColorVG_Qo__AE4TextVQo__Qo__SbQo__A11_A11_tGyXEfU_', symObjAddr: 0x29DC, symBinAddr: 0x10000D814, symSize: 0x750 }
  - { offset: 0xAEA39, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0A10StringViewV7SwiftUI0D0AadEP4body4BodyQzvgTW', symObjAddr: 0x314C, symBinAddr: 0x10000DF84, symSize: 0x1C4 }
  - { offset: 0xAEACE, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewV4bodyQrvg7SwiftUI05TupleE0VyAE6ButtonVyAE0E0PAEE12cornerRadius_11antialiasedQr12CoreGraphics7CGFloatV_SbtFQOyAkEE15foregroundColoryQrAE0R0VSgFQOyAkEE10background_20ignoresSafeAreaEdgesQrqd___AE4EdgeO3SetVtAE10ShapeStyleRd__lFQOyAkEE7paddingyQrAZ_APSgtFQOyAE6HStackVyAGyAE5ImageV_AE4TextVtGG_Qo__ASQo__Qo__Qo_G_A14_tGyXEfU_', symObjAddr: 0x3310, symBinAddr: 0x10000E148, symSize: 0x1A8 }
  - { offset: 0xAEB03, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewV4bodyQrvg7SwiftUI05TupleE0VyAE6ButtonVyAE0E0PAEE12cornerRadius_11antialiasedQr12CoreGraphics7CGFloatV_SbtFQOyAkEE15foregroundColoryQrAE0R0VSgFQOyAkEE10background_20ignoresSafeAreaEdgesQrqd___AE4EdgeO3SetVtAE10ShapeStyleRd__lFQOyAkEE7paddingyQrAZ_APSgtFQOyAE6HStackVyAGyAE5ImageV_AE4TextVtGG_Qo__ASQo__Qo__Qo_G_A14_tGyXEfU_A13_yXEfU_', symObjAddr: 0x34B8, symBinAddr: 0x10000E2F0, symSize: 0x240 }
  - { offset: 0xAEC4F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewV4bodyQrvg7SwiftUI05TupleE0VyAE6ButtonVyAE0E0PAEE12cornerRadius_11antialiasedQr12CoreGraphics7CGFloatV_SbtFQOyAkEE15foregroundColoryQrAE0R0VSgFQOyAkEE10background_20ignoresSafeAreaEdgesQrqd___AE4EdgeO3SetVtAE10ShapeStyleRd__lFQOyAkEE7paddingyQrAZ_APSgtFQOyAE6HStackVyAGyAE5ImageV_AE4TextVtGG_Qo__ASQo__Qo__Qo_G_A14_tGyXEfU_A13_yXEfU_A8_yXEfU_', symObjAddr: 0x36F8, symBinAddr: 0x10000E530, symSize: 0xFC }
  - { offset: 0xAEC8B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewV4bodyQrvg7SwiftUI05TupleE0VyAE6ButtonVyAE0E0PAEE12cornerRadius_11antialiasedQr12CoreGraphics7CGFloatV_SbtFQOyAkEE15foregroundColoryQrAE0R0VSgFQOyAkEE10background_20ignoresSafeAreaEdgesQrqd___AE4EdgeO3SetVtAE10ShapeStyleRd__lFQOyAkEE7paddingyQrAZ_APSgtFQOyAE6HStackVyAGyAE5ImageV_AE4TextVtGG_Qo__ASQo__Qo__Qo_G_A14_tGyXEfU_A13_yXEfU0_', symObjAddr: 0x37F4, symBinAddr: 0x10000E62C, symSize: 0x2AC }
  - { offset: 0xAEDFC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewV4bodyQrvg7SwiftUI05TupleE0VyAE6ButtonVyAE0E0PAEE12cornerRadius_11antialiasedQr12CoreGraphics7CGFloatV_SbtFQOyAkEE15foregroundColoryQrAE0R0VSgFQOyAkEE10background_20ignoresSafeAreaEdgesQrqd___AE4EdgeO3SetVtAE10ShapeStyleRd__lFQOyAkEE7paddingyQrAZ_APSgtFQOyAE6HStackVyAGyAE5ImageV_AE4TextVtGG_Qo__ASQo__Qo__Qo_G_A14_tGyXEfU_A13_yXEfU0_A8_yXEfU_', symObjAddr: 0x3AA0, symBinAddr: 0x10000E8D8, symSize: 0x114 }
  - { offset: 0xAEE80, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18ControlButtonsViewV7SwiftUI0E0AadEP4body4BodyQzvgTW', symObjAddr: 0x3BB4, symBinAddr: 0x10000E9EC, symSize: 0x60 }
  - { offset: 0xAEF4D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_', symObjAddr: 0x3C14, symBinAddr: 0x10000EA4C, symSize: 0x3C8 }
  - { offset: 0xAEFBF, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_A20_AZcfU_', symObjAddr: 0x3FDC, symBinAddr: 0x10000EE14, symSize: 0x114 }
  - { offset: 0xAF019, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_A20_AZcfU_yyScMYccfU_', symObjAddr: 0x40F0, symBinAddr: 0x10000EF28, symSize: 0xC0 }
  - { offset: 0xAF090, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_A20_AZcfU_A19_yXEfU0_', symObjAddr: 0x41B0, symBinAddr: 0x10000EFE8, symSize: 0xE8 }
  - { offset: 0xAF16A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_A20_AZcfU_A19_yXEfU0_A18_yXEfU_', symObjAddr: 0x4298, symBinAddr: 0x10000F0D0, symSize: 0x1E0 }
  - { offset: 0xAF272, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_A32_yXEfU0_', symObjAddr: 0x4478, symBinAddr: 0x10000F2B0, symSize: 0x128 }
  - { offset: 0xAF297, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_A32_yXEfU0_A30_yXEfU_', symObjAddr: 0x45A0, symBinAddr: 0x10000F3D8, symSize: 0xF8 }
  - { offset: 0xAF2BC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV4bodyQrvg7SwiftUI0E0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationN4ItemV0opQ0OFQOyAgEE0mO0yQrAE18LocalizedStringKeyVFQOyAE4ListVys5NeverOAE7ForEachVySayAA0aC0OGAzE6ButtonVyAE6HStackVyAE05TupleE0VyAE4TextV_AE6SpacerVAgEE15foregroundColoryQrAE5ColorVSgFQOyAE5ImageV_Qo_SgtGGGGG_Qo__Qo__AE0kL7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0kS0VyytA1_yA7_GGQo_Qo_yXEfU_A32_yXEfU0_A30_yXEfU_yyScMYccfU_', symObjAddr: 0x4698, symBinAddr: 0x10000F4D0, symSize: 0x88 }
  - { offset: 0xAF2FB, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner18TuningSelectorViewV7SwiftUI0E0AadEP4body4BodyQzvgTW', symObjAddr: 0x4760, symBinAddr: 0x10000F598, symSize: 0x15C }
  - { offset: 0xAF330, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI11EnvironmentV12wrappedValuexvgAA13DismissActionV_Tg5', symObjAddr: 0x5574, symBinAddr: 0x1000103A8, symSize: 0x1E4 }
  - { offset: 0xAF7D5, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MainViewModelC11isRecordingSbvpACTK', symObjAddr: 0x7E0, symBinAddr: 0x100012C34, symSize: 0x7C }
  - { offset: 0xAF803, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MainViewModelC11isRecordingSbvpACTk', symObjAddr: 0x85C, symBinAddr: 0x100012CB0, symSize: 0x70 }
  - { offset: 0xAF839, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MainViewModelCMU', symObjAddr: 0xC60, symBinAddr: 0x1000130B4, symSize: 0x8 }
  - { offset: 0xAF84D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MainViewModelCMa', symObjAddr: 0xC68, symBinAddr: 0x1000130BC, symSize: 0x38 }
  - { offset: 0xAF861, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MainViewModelCMr', symObjAddr: 0xCA0, symBinAddr: 0x1000130F4, symSize: 0xFC }
  - { offset: 0xAF875, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner3TabOwet', symObjAddr: 0xDF0, symBinAddr: 0x1000131F0, symSize: 0x90 }
  - { offset: 0xAF889, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner3TabOwst', symObjAddr: 0xE80, symBinAddr: 0x100013280, symSize: 0xB0 }
  - { offset: 0xAF89D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner3TabOMa', symObjAddr: 0xF44, symBinAddr: 0x100013330, symSize: 0x10 }
  - { offset: 0xAF8B1, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner3TabOs12CaseIterableAA8AllCasessADP_SlWT', symObjAddr: 0xF54, symBinAddr: 0x100013340, symSize: 0x2C }
  - { offset: 0xAF8C5, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner3TabOSHAASQWb', symObjAddr: 0x1120, symBinAddr: 0x100013404, symSize: 0x4 }
  - { offset: 0xAF8D9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner3TabOACSQAAWl', symObjAddr: 0x1124, symBinAddr: 0x100013408, symSize: 0x40 }
  - { offset: 0xAF8ED, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerC8settingsAA04UserC0VvpACTK', symObjAddr: 0x12C0, symBinAddr: 0x1000134DC, symSize: 0x9C }
  - { offset: 0xAF91B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15SettingsManagerC8settingsAA04UserC0VvpACTk', symObjAddr: 0x135C, symBinAddr: 0x100013578, symSize: 0xAC }
  - { offset: 0xAF978, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MainViewModelC13setupBindings33_2A3E349D7778E769813F8B3543D8D9FBLLyyFyAA0A6TuningOcfU_TA', symObjAddr: 0x1484, symBinAddr: 0x1000136A0, symSize: 0x8 }
  - { offset: 0xAF9CD, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MainViewModelC11isRecordingSbvg', symObjAddr: 0x90, symBinAddr: 0x1000124E4, symSize: 0x70 }
  - { offset: 0xAFA70, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MainViewModelC7Combine16ObservableObjectAadEP16objectWillChange0hjK9PublisherQzvgTW', symObjAddr: 0xFC8, symBinAddr: 0x10001336C, symSize: 0x3C }
  - { offset: 0xAFB3D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MainViewModelCACycfc', symObjAddr: 0x100, symBinAddr: 0x100012554, symSize: 0x2D8 }
  - { offset: 0xAFD0A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MainViewModelC13setupBindings33_2A3E349D7778E769813F8B3543D8D9FBLLyyF', symObjAddr: 0x3D8, symBinAddr: 0x10001282C, symSize: 0x408 }
  - { offset: 0xAFD8C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MainViewModelC13setupBindings33_2A3E349D7778E769813F8B3543D8D9FBLLyyFyAA0A6TuningOcfU_', symObjAddr: 0x8F4, symBinAddr: 0x100012D48, symSize: 0x80 }
  - { offset: 0xAFE4D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MainViewModelC9setTuningyyAA0aG0OF', symObjAddr: 0x974, symBinAddr: 0x100012DC8, symSize: 0x1EC }
  - { offset: 0xAFF8E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MainViewModelCfD', symObjAddr: 0xB60, symBinAddr: 0x100012FB4, symSize: 0x100 }
  - { offset: 0xAFFDC, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner3TabOSYAASY8rawValuexSg03RawE0Qz_tcfCTW', symObjAddr: 0x10C4, symBinAddr: 0x1000133A8, symSize: 0x18 }
  - { offset: 0xB0005, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner3TabOSYAASY8rawValue03RawE0QzvgTW', symObjAddr: 0x10DC, symBinAddr: 0x1000133C0, symSize: 0xC }
  - { offset: 0xB0026, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner3TabOs12CaseIterableAAsADP8allCases03AllG0QzvgZTW', symObjAddr: 0x10E8, symBinAddr: 0x1000133CC, symSize: 0x38 }
  - { offset: 0xB0213, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI11StateObjectV7StorageOy11GuitarTuner13MainViewModelC_GWOy', symObjAddr: 0x38, symBinAddr: 0x100013728, symSize: 0x8 }
  - { offset: 0xB0227, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner11ContentViewVwxx', symObjAddr: 0x40, symBinAddr: 0x100013730, symSize: 0x10 }
  - { offset: 0xB023B, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI11StateObjectV7StorageOy11GuitarTuner13MainViewModelC_GWOe', symObjAddr: 0x50, symBinAddr: 0x100013740, symSize: 0x8 }
  - { offset: 0xB024F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner11ContentViewVwca', symObjAddr: 0xA0, symBinAddr: 0x100013790, symSize: 0x54 }
  - { offset: 0xB0263, size: 0x8, addend: 0x0, symName: ___swift_memcpy17_8, symObjAddr: 0xF4, symBinAddr: 0x1000137E4, symSize: 0x14 }
  - { offset: 0xB0277, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner11ContentViewVwta', symObjAddr: 0x108, symBinAddr: 0x1000137F8, symSize: 0x44 }
  - { offset: 0xB028B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner11ContentViewVwet', symObjAddr: 0x14C, symBinAddr: 0x10001383C, symSize: 0x48 }
  - { offset: 0xB029F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner11ContentViewVwst', symObjAddr: 0x194, symBinAddr: 0x100013884, symSize: 0x44 }
  - { offset: 0xB02B3, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner11ContentViewVMa', symObjAddr: 0x1D8, symBinAddr: 0x1000138C8, symSize: 0x10 }
  - { offset: 0xB02C7, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner11ContentViewV7SwiftUI0D0AA4BodyAdEP_AGWT', symObjAddr: 0x1E8, symBinAddr: 0x1000138D8, symSize: 0x10 }
  - { offset: 0xB0457, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MainViewModelCAC7Combine16ObservableObjectAAWl', symObjAddr: 0xBA0, symBinAddr: 0x100014270, symSize: 0x44 }
  - { offset: 0xB046B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner11ContentViewV4bodyQrvg7SwiftUI05TupleD0VyAE0D0PAEE3tag_15includeOptionalQrqd___SbtSHRd__lFQOyAiEE7tabItemyQrqd__yXEAeHRd__lFQOyAA011QuickTuningD0V_AGyAE5ImageV_AE4TextVtGQo__AA3TabOQo__AiEEAJ_AKQrqd___SbtSHRd__lFQOyAiEEALyQrqd__yXEAeHRd__lFQOyAA012ProfessionaloD0V_ASQo__AVQo_AiEEAJ_AKQrqd___SbtSHRd__lFQOyAiEEALyQrqd__yXEAeHRd__lFQOyAR_ASQo__AVQo_tGyXEfU_TA', symObjAddr: 0xBEC, symBinAddr: 0x1000142BC, symSize: 0xC }
  - { offset: 0xB047F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner3TabOACSHAAWl', symObjAddr: 0xC3C, symBinAddr: 0x1000142C8, symSize: 0x40 }
  - { offset: 0xB0493, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15QuickTuningViewVAC7SwiftUI0E0AAWl', symObjAddr: 0xCC4, symBinAddr: 0x100014308, symSize: 0x40 }
  - { offset: 0xB04A7, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewVAC7SwiftUI0E0AAWl', symObjAddr: 0xD04, symBinAddr: 0x100014348, symSize: 0x40 }
  - { offset: 0xB04C9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner11ContentViewV10_viewModel33_0264E43E3DDAB47D2719C1231FC067EBLL7SwiftUI11StateObjectVyAA04MaindF0CGvpfiAJycfu_AJycfu0_', symObjAddr: 0x0, symBinAddr: 0x1000136F0, symSize: 0x34 }
  - { offset: 0xB0706, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner11ContentViewV4bodyQrvg7SwiftUI05TupleD0VyAE0D0PAEE3tag_15includeOptionalQrqd___SbtSHRd__lFQOyAiEE7tabItemyQrqd__yXEAeHRd__lFQOyAA011QuickTuningD0V_AGyAE5ImageV_AE4TextVtGQo__AA3TabOQo__AiEEAJ_AKQrqd___SbtSHRd__lFQOyAiEEALyQrqd__yXEAeHRd__lFQOyAA012ProfessionaloD0V_ASQo__AVQo_AiEEAJ_AKQrqd___SbtSHRd__lFQOyAiEEALyQrqd__yXEAeHRd__lFQOyAR_ASQo__AVQo_tGyXEfU_', symObjAddr: 0x1F8, symBinAddr: 0x1000138E8, symSize: 0x70C }
  - { offset: 0xB09A3, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner11ContentViewV7SwiftUI0D0AadEP4body4BodyQzvgTW', symObjAddr: 0xA84, symBinAddr: 0x100014154, symSize: 0x11C }
  - { offset: 0xB0ADD, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0aB3AppV4bodyQrvgAA11ContentViewVycfU_', symObjAddr: 0x0, symBinAddr: 0x1000143B8, symSize: 0x14 }
  - { offset: 0xB0B76, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0aB3AppVAC7SwiftUI0C0AAWl', symObjAddr: 0x108, symBinAddr: 0x1000144BC, symSize: 0x40 }
  - { offset: 0xB0B8A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0aB3AppVMa', symObjAddr: 0x148, symBinAddr: 0x1000144FC, symSize: 0x10 }
  - { offset: 0xB0B9E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0aB3AppV7SwiftUI0C0AA4BodyAdEP_AD5ScenePWT', symObjAddr: 0x158, symBinAddr: 0x10001450C, symSize: 0x10 }
  - { offset: 0xB0BB2, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner11ContentViewVAC7SwiftUI0D0AAWl', symObjAddr: 0x1AC, symBinAddr: 0x10001451C, symSize: 0x40 }
  - { offset: 0xB0BC6, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI11WindowGroupVy11GuitarTuner11ContentViewVGACyxGAA5SceneAAWl', symObjAddr: 0x1EC, symBinAddr: 0x10001455C, symSize: 0x48 }
  - { offset: 0xB0BDB, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0aB3AppV4bodyQrvgAA11ContentViewVycfU_', symObjAddr: 0x0, symBinAddr: 0x1000143B8, symSize: 0x14 }
  - { offset: 0xB0C2D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner0aB3AppV7SwiftUI0C0AadEP4body4BodyQzvgTW', symObjAddr: 0x14, symBinAddr: 0x1000143CC, symSize: 0xBC }
  - { offset: 0xB0C9D, size: 0x8, addend: 0x0, symName: _main, symObjAddr: 0xD4, symBinAddr: 0x100014488, symSize: 0x34 }
  - { offset: 0xB0E17, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewVMa', symObjAddr: 0x1B8, symBinAddr: 0x1000145A8, symSize: 0x10 }
  - { offset: 0xB0E2B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewV7SwiftUI0E0AA4BodyAdEP_AGWT', symObjAddr: 0x1C8, symBinAddr: 0x1000145B8, symSize: 0x10 }
  - { offset: 0xB10E4, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewV4bodyQrvg7SwiftUI0E0PAEE19navigationBarHiddenyQrSbFQOyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE6VStackVyAE05TupleE0VyAgEEAIyQrAM_AQtFQOyAE6HStackVyAUyAE4TextV_AE6SpacerVAE6ButtonVyAgEE15foregroundColoryQrAE0Y0VSgFQOyAgEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo_GtGG_Qo__AA016FrequencyDisplayE0VAA0cb5MeterE0VAA010StringInfoE0VSgA_AA014ControlButtonsE0VtGG_Qo__Qo_yXEfU_TA', symObjAddr: 0x1114, symBinAddr: 0x1000154C0, symSize: 0x10 }
  - { offset: 0xB10F8, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6VStackVyAA9TupleViewVyACyAA6HStackVyAGyAA4TextV_AA6SpacerVAA6ButtonVyACyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGASyAA5ColorVSgGGGtGGAA14_PaddingLayoutVG_11GuitarTuner016FrequencyDisplayG0VA8_012Professionalv5MeterG0VA8_010StringInfoG0VSgAMA8_014ControlButtonsG0VtGGA6_GACyxq_GAA0G0A2AA22_RzAA0gP0R_rlWl', symObjAddr: 0x116C, symBinAddr: 0x1000154D0, symSize: 0x88 }
  - { offset: 0xB110C, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewV4bodyQrvgAA08SettingsE0VycfU0_TA', symObjAddr: 0x11F8, symBinAddr: 0x10001555C, symSize: 0x10 }
  - { offset: 0xB1120, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewVMa', symObjAddr: 0x1208, symBinAddr: 0x10001556C, symSize: 0x38 }
  - { offset: 0xB15EA, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI17EnvironmentValuesV15foregroundColorAA0F0VSgvpACTKq', symObjAddr: 0x4CEC, symBinAddr: 0x100018FF0, symSize: 0x28 }
  - { offset: 0xB15FE, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI17EnvironmentValuesV15foregroundColorAA0F0VSgvpACTkq', symObjAddr: 0x4D14, symBinAddr: 0x100019018, symSize: 0x28 }
  - { offset: 0xB161D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewV4bodyQrvg7SwiftUI0E0PAEE19navigationBarHiddenyQrSbFQOyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE6VStackVyAE05TupleE0VyAgEEAIyQrAM_AQtFQOyAE6HStackVyAUyAE4TextV_AE6SpacerVAE6ButtonVyAgEE15foregroundColoryQrAE0Y0VSgFQOyAgEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo_GtGG_Qo__AA016FrequencyDisplayE0VAA0cb5MeterE0VAA010StringInfoE0VSgA_AA014ControlButtonsE0VtGG_Qo__Qo_yXEfU_A27_yXEfU_yycAA04MainE5ModelCcfu_yycfu0_TA', symObjAddr: 0x4E88, symBinAddr: 0x10001918C, symSize: 0x8 }
  - { offset: 0xB1631, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewVWOr', symObjAddr: 0x4E90, symBinAddr: 0x100019194, symSize: 0x28 }
  - { offset: 0xB1645, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewVWOs', symObjAddr: 0x4EB8, symBinAddr: 0x1000191BC, symSize: 0x28 }
  - { offset: 0xB1659, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAGyAA5ColorVSgGGACyxq_GAA4ViewA2aSRzAA0lI0R_rlWl', symObjAddr: 0x4F7C, symBinAddr: 0x1000191F0, symSize: 0x88 }
  - { offset: 0xB166D, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGACyxq_GAA4ViewA2aNRzAA0kI0R_rlWl', symObjAddr: 0x5004, symBinAddr: 0x100019278, symSize: 0x88 }
  - { offset: 0xB1697, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewVwCP', symObjAddr: 0x513C, symBinAddr: 0x1000193A0, symSize: 0xD8 }
  - { offset: 0xB16AB, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewVwxx', symObjAddr: 0x5214, symBinAddr: 0x100019478, symSize: 0x74 }
  - { offset: 0xB16BF, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewVwcp', symObjAddr: 0x5288, symBinAddr: 0x1000194EC, symSize: 0xA4 }
  - { offset: 0xB16D3, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewVwca', symObjAddr: 0x532C, symBinAddr: 0x100019590, symSize: 0xD4 }
  - { offset: 0xB16E7, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewVwtk', symObjAddr: 0x5400, symBinAddr: 0x100019664, symSize: 0xA4 }
  - { offset: 0xB16FB, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewVwta', symObjAddr: 0x54A4, symBinAddr: 0x100019708, symSize: 0xC8 }
  - { offset: 0xB170F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewVwet', symObjAddr: 0x556C, symBinAddr: 0x1000197D0, symSize: 0xC }
  - { offset: 0xB1723, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewVwst', symObjAddr: 0x55F8, symBinAddr: 0x10001985C, symSize: 0xC }
  - { offset: 0xB1737, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewVMr', symObjAddr: 0x567C, symBinAddr: 0x1000198E0, symSize: 0x74 }
  - { offset: 0xB174B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14StringInfoViewVMa', symObjAddr: 0x5858, symBinAddr: 0x100019954, symSize: 0x10 }
  - { offset: 0xB175F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewVwxx', symObjAddr: 0x5894, symBinAddr: 0x100019964, symSize: 0x8 }
  - { offset: 0xB1773, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewVwcp', symObjAddr: 0x589C, symBinAddr: 0x10001996C, symSize: 0x5C }
  - { offset: 0xB1787, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewVwca', symObjAddr: 0x58F8, symBinAddr: 0x1000199C8, symSize: 0x8C }
  - { offset: 0xB179B, size: 0x8, addend: 0x0, symName: ___swift_memcpy60_8, symObjAddr: 0x5984, symBinAddr: 0x100019A54, symSize: 0x1C }
  - { offset: 0xB17AF, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewVwta', symObjAddr: 0x59A0, symBinAddr: 0x100019A70, symSize: 0x6C }
  - { offset: 0xB17C3, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewVwet', symObjAddr: 0x5A0C, symBinAddr: 0x100019ADC, symSize: 0x5C }
  - { offset: 0xB17D7, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewVwst', symObjAddr: 0x5A68, symBinAddr: 0x100019B38, symSize: 0x64 }
  - { offset: 0xB17EB, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewVMa', symObjAddr: 0x5ACC, symBinAddr: 0x100019B9C, symSize: 0x10 }
  - { offset: 0xB17FF, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewV7SwiftUI0E0AA4BodyAdEP_AGWT', symObjAddr: 0x5B7C, symBinAddr: 0x100019C4C, symSize: 0x10 }
  - { offset: 0xB1813, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14StringInfoViewV7SwiftUI0E0AA4BodyAdEP_AGWT', symObjAddr: 0x5B8C, symBinAddr: 0x100019C5C, symSize: 0x10 }
  - { offset: 0xB1827, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewV7SwiftUI0D0AA4BodyAdEP_AGWT', symObjAddr: 0x5B9C, symBinAddr: 0x100019C6C, symSize: 0x10 }
  - { offset: 0xB183B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewV4bodyQrvg7SwiftUI0D0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationM4ItemV0noP0OFQOyAgEE0lN0yQrAE18LocalizedStringKeyVFQOyAE6VStackVyAE05TupleD0VyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE4TextV_Qo__AE6SpacerVtGG_Qo__Qo__AE0jK7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0jR0VyytAE6ButtonVyA5_GGQo_Qo_yXEfU_TA', symObjAddr: 0x5BAC, symBinAddr: 0x100019C7C, symSize: 0x8 }
  - { offset: 0xB184F, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackVyAA9TupleViewVyAA15ModifiedContentVyAA4TextVAA14_PaddingLayoutVG_AA6SpacerVtGGWOs', symObjAddr: 0x5BB4, symBinAddr: 0x100019C84, symSize: 0x3C }
  - { offset: 0xB1863, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewV4bodyQrvg7SwiftUI0D0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationM4ItemV0noP0OFQOyAgEE0lN0yQrAE18LocalizedStringKeyVFQOyAE6VStackVyAE05TupleD0VyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE4TextV_Qo__AE6SpacerVtGG_Qo__Qo__AE0jK7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0jR0VyytAE6ButtonVyA5_GGQo_Qo_yXEfU_A22_yXEfU0_TA', symObjAddr: 0x5BF0, symBinAddr: 0x100019CC0, symSize: 0x8 }
  - { offset: 0xB1877, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewV4bodyQrvg7SwiftUI0D0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationM4ItemV0noP0OFQOyAgEE0lN0yQrAE18LocalizedStringKeyVFQOyAE6VStackVyAE05TupleD0VyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE4TextV_Qo__AE6SpacerVtGG_Qo__Qo__AE0jK7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0jR0VyytAE6ButtonVyA5_GGQo_Qo_yXEfU_A22_yXEfU0_A20_yXEfU_TA', symObjAddr: 0x5BF8, symBinAddr: 0x100019CC8, symSize: 0x8 }
  - { offset: 0xB188B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewVWOb', symObjAddr: 0x5CAC, symBinAddr: 0x100019D7C, symSize: 0x44 }
  - { offset: 0xB189F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewV4bodyQrvg7SwiftUI0D0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationM4ItemV0noP0OFQOyAgEE0lN0yQrAE18LocalizedStringKeyVFQOyAE6VStackVyAE05TupleD0VyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE4TextV_Qo__AE6SpacerVtGG_Qo__Qo__AE0jK7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0jR0VyytAE6ButtonVyA5_GGQo_Qo_yXEfU_A22_yXEfU0_A20_yXEfU_yyScMYccfU_TA', symObjAddr: 0x5CF0, symBinAddr: 0x100019DC0, symSize: 0x2C }
  - { offset: 0xB18B3, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA4TextV_AA6HStackVyACyAE_A2EtGGtGWOr', symObjAddr: 0x5D94, symBinAddr: 0x100019E64, symSize: 0xD8 }
  - { offset: 0xB18C7, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA4TextV_AA6HStackVyACyAE_A2EtGGtGWOs', symObjAddr: 0x5E6C, symBinAddr: 0x100019F3C, symSize: 0xD8 }
  - { offset: 0xB18DB, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6VStackVyAA9TupleViewVyAA4TextV_AA6HStackVyAGyAI_A2ItGGtGGAA14_PaddingLayoutVGWOr', symObjAddr: 0x5FC4, symBinAddr: 0x10001A014, symSize: 0xD8 }
  - { offset: 0xB18EF, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6VStackVyAA9TupleViewVyAA4TextV_AA6HStackVyAGyAI_A2ItGGtGGAA14_PaddingLayoutVGWOs', symObjAddr: 0x609C, symBinAddr: 0x10001A0EC, symSize: 0xD8 }
  - { offset: 0xB1903, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA4TextV_A2EtGWOr', symObjAddr: 0x6174, symBinAddr: 0x10001A1C4, symSize: 0xA4 }
  - { offset: 0xB1917, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA4TextV_A2EtGWOs', symObjAddr: 0x6218, symBinAddr: 0x10001A268, symSize: 0xA4 }
  - { offset: 0xB192B, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6HStackVyAA9TupleViewVyAA4TextV_A2GtGGWOr', symObjAddr: 0x62BC, symBinAddr: 0x10001A30C, symSize: 0xA4 }
  - { offset: 0xB193F, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6HStackVyAA9TupleViewVyAA4TextV_A2GtGGWOs', symObjAddr: 0x6360, symBinAddr: 0x10001A3B0, symSize: 0xA4 }
  - { offset: 0xB1953, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVy11GuitarTuner015MeterBackgroundD0V_AD0g5ScaleD0VAD0g6NeedleD0VAA15ModifiedContentVyAA06_ShapeD0VyAA6CircleVAA5ColorVGAA12_FrameLayoutVGAA6VStackVyACyAA6SpacerV_ALyAA6HStackVyACyAXyACyAA4TextV_A2_tGG_A4_SgA4_tGGAA08_PaddingQ0VGtGGtGWOr', symObjAddr: 0x6444, symBinAddr: 0x10001A454, symSize: 0x140 }
  - { offset: 0xB1967, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackVyAA9TupleViewVyAA4TextV_AGtGGSgWOy', symObjAddr: 0x6584, symBinAddr: 0x10001A594, symSize: 0x6C }
  - { offset: 0xB197B, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVy11GuitarTuner015MeterBackgroundD0V_AD0g5ScaleD0VAD0g6NeedleD0VAA15ModifiedContentVyAA06_ShapeD0VyAA6CircleVAA5ColorVGAA12_FrameLayoutVGAA6VStackVyACyAA6SpacerV_ALyAA6HStackVyACyAXyACyAA4TextV_A2_tGG_A4_SgA4_tGGAA08_PaddingQ0VGtGGtGWOs', symObjAddr: 0x65F0, symBinAddr: 0x10001A600, symSize: 0x140 }
  - { offset: 0xB198F, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackVyAA9TupleViewVyAA4TextV_AGtGGSgWOe', symObjAddr: 0x6730, symBinAddr: 0x10001A740, symSize: 0x6C }
  - { offset: 0xB19A3, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewV4bodyQrvgySf_SftcfU0_TA', symObjAddr: 0x67C0, symBinAddr: 0x10001A7D0, symSize: 0x8 }
  - { offset: 0xB19B7, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6ZStackVyAA9TupleViewVy11GuitarTuner015MeterBackgroundG0V_AH0j5ScaleG0VAH0j6NeedleG0VACyAA06_ShapeG0VyAA6CircleVAA5ColorVGAA12_FrameLayoutVGAA6VStackVyAGyAA6SpacerV_ACyAA6HStackVyAGyAZyAGyAA4TextV_A4_tGG_A6_SgA6_tGGAA08_PaddingR0VGtGGtGGAWGACyxq_GAA0G0A2AA19_RzAA0G8ModifierR_rlWl', symObjAddr: 0x67C8, symBinAddr: 0x10001A7D8, symSize: 0x88 }
  - { offset: 0xB19CB, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6ZStackVyAA9TupleViewVy11GuitarTuner015MeterBackgroundG0V_AH0j5ScaleG0VAH0j6NeedleG0VACyAA06_ShapeG0VyAA6CircleVAA5ColorVGAA12_FrameLayoutVGAA6VStackVyAGyAA6SpacerV_ACyAA6HStackVyAGyAZyAGyAA4TextV_A4_tGG_A6_SgA6_tGGAA08_PaddingR0VGtGGtGGAWGWOs', symObjAddr: 0x6850, symBinAddr: 0x10001A860, symSize: 0x140 }
  - { offset: 0xB19DF, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewV17updateNeedleAngle33_0D9BF467B77FBB78DFB3F2C723072EC3LL5centsySf_tFyyXEfU_TA', symObjAddr: 0x6990, symBinAddr: 0x10001A9A0, symSize: 0x58 }
  - { offset: 0xB1A1D, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA6SpacerV_AA15ModifiedContentVyAA6HStackVyACyAA6VStackVyACyAA4TextV_AMtGG_AOSgAOtGGAA14_PaddingLayoutVGtGWOr', symObjAddr: 0x69E8, symBinAddr: 0x10001A9F8, symSize: 0x130 }
  - { offset: 0xB1A31, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA6SpacerV_AA15ModifiedContentVyAA6HStackVyACyAA6VStackVyACyAA4TextV_AMtGG_AOSgAOtGGAA14_PaddingLayoutVGtGWOs', symObjAddr: 0x6B18, symBinAddr: 0x10001AB28, symSize: 0x130 }
  - { offset: 0xB1A45, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackVyAA9TupleViewVyAA6SpacerV_AA15ModifiedContentVyAA6HStackVyAEyACyAEyAA4TextV_AMtGG_AOSgAOtGGAA14_PaddingLayoutVGtGGWOr', symObjAddr: 0x6C70, symBinAddr: 0x10001AC58, symSize: 0x130 }
  - { offset: 0xB1A59, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackVyAA9TupleViewVyAA6SpacerV_AA15ModifiedContentVyAA6HStackVyAEyACyAEyAA4TextV_AMtGG_AOSgAOtGGAA14_PaddingLayoutVGtGGWOs', symObjAddr: 0x6DA0, symBinAddr: 0x10001AD88, symSize: 0x130 }
  - { offset: 0xB1A6D, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA6VStackVyACyAA4TextV_AGtGG_AISgAItGWOr', symObjAddr: 0x6EF8, symBinAddr: 0x10001AEB8, symSize: 0x130 }
  - { offset: 0xB1A81, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA6VStackVyACyAA4TextV_AGtGG_AISgAItGWOs', symObjAddr: 0x7028, symBinAddr: 0x10001AFE8, symSize: 0x130 }
  - { offset: 0xB1A95, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6HStackVyAA9TupleViewVyAA6VStackVyAGyAA4TextV_AKtGG_AMSgAMtGGAA14_PaddingLayoutVGWOr', symObjAddr: 0x7158, symBinAddr: 0x10001B118, symSize: 0x130 }
  - { offset: 0xB1AA9, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6HStackVyAA9TupleViewVyAA6VStackVyAGyAA4TextV_AKtGG_AMSgAMtGGAA14_PaddingLayoutVGWOs', symObjAddr: 0x7288, symBinAddr: 0x10001B248, symSize: 0x130 }
  - { offset: 0xB1ABD, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackVyAA9TupleViewVyAA4TextV_AGtGGWOr', symObjAddr: 0x73B8, symBinAddr: 0x10001B378, symSize: 0x70 }
  - { offset: 0xB1AD1, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackVyAA9TupleViewVyAA4TextV_AGtGGWOs', symObjAddr: 0x7428, symBinAddr: 0x10001B3E8, symSize: 0x70 }
  - { offset: 0xB1AE5, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15MeterNeedleViewVwet', symObjAddr: 0x7500, symBinAddr: 0x10001B4AC, symSize: 0x44 }
  - { offset: 0xB1AF9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15MeterNeedleViewVwst', symObjAddr: 0x7544, symBinAddr: 0x10001B4F0, symSize: 0x44 }
  - { offset: 0xB1B0D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15MeterNeedleViewVMa', symObjAddr: 0x7588, symBinAddr: 0x10001B534, symSize: 0x10 }
  - { offset: 0xB1B21, size: 0x8, addend: 0x0, symName: ___swift_memcpy12_8, symObjAddr: 0x7598, symBinAddr: 0x10001B544, symSize: 0x14 }
  - { offset: 0xB1B35, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterScaleViewVwet', symObjAddr: 0x75AC, symBinAddr: 0x10001B558, symSize: 0x20 }
  - { offset: 0xB1B49, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterScaleViewVwst', symObjAddr: 0x75CC, symBinAddr: 0x10001B578, symSize: 0x2C }
  - { offset: 0xB1B5D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterScaleViewVMa', symObjAddr: 0x75F8, symBinAddr: 0x10001B5A4, symSize: 0x10 }
  - { offset: 0xB1B71, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner19MeterBackgroundViewVMa', symObjAddr: 0x7608, symBinAddr: 0x10001B5B4, symSize: 0x10 }
  - { offset: 0xB1B85, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA6VStackVyAA9TupleViewVyAA4TextV_AA6HStackVyAGyAI_A2ItGGtGGAA14_PaddingLayoutVGAA19_BackgroundModifierVyAA06_ShapeG0VyAA16RoundedRectangleVAA5ColorVGGGACyxq_GAA0G0A2AA3_RzAA0gM0R_rlWl', symObjAddr: 0x7648, symBinAddr: 0x10001B5F4, symSize: 0x88 }
  - { offset: 0xB1B99, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6VStackVyAA9TupleViewVyAA4TextV_AA6HStackVyAGyAI_A2ItGGtGGAA14_PaddingLayoutVGACyxq_GAA0G0A2aTRzAA0G8ModifierR_rlWl', symObjAddr: 0x76D0, symBinAddr: 0x10001B67C, symSize: 0x88 }
  - { offset: 0xB1BAD, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner19MeterBackgroundViewV7SwiftUI0E0AA4BodyAdEP_AGWT', symObjAddr: 0x77B8, symBinAddr: 0x10001B764, symSize: 0x10 }
  - { offset: 0xB1BC1, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterScaleViewV7SwiftUI0E0AA4BodyAdEP_AGWT', symObjAddr: 0x77C8, symBinAddr: 0x10001B774, symSize: 0x10 }
  - { offset: 0xB1BD5, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15MeterNeedleViewV7SwiftUI0E0AA4BodyAdEP_AGWT', symObjAddr: 0x77D8, symBinAddr: 0x10001B784, symSize: 0x10 }
  - { offset: 0xB1BE9, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA10_ShapeViewVyAA16RoundedRectangleVAA5ColorVGAA12_FrameLayoutVGAA13_OffsetEffectVGWOb', symObjAddr: 0x77E8, symBinAddr: 0x10001B794, symSize: 0x48 }
  - { offset: 0xB1BFD, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterScaleViewV4bodyQrvg7SwiftUI05TupleE0VyAE7ForEachVySNySiGSiAGyAA0c4TickE0V_AA0c5LabelE0VSgtGG_AIyAJSiALSgGtGyXEfU_APSicfU_TA', symObjAddr: 0x78C0, symBinAddr: 0x10001B7EC, symSize: 0xC }
  - { offset: 0xB1C11, size: 0x8, addend: 0x0, symName: '_$sSNySiGSNyxGSksSxRzSZ6StrideRpzrlWl', symObjAddr: 0x78CC, symBinAddr: 0x10001B7F8, symSize: 0x70 }
  - { offset: 0xB1C25, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterScaleViewV4bodyQrvg7SwiftUI05TupleE0VyAE7ForEachVySNySiGSiAGyAA0c4TickE0V_AA0c5LabelE0VSgtGG_AIyAJSiALSgGtGyXEfU_ARSicfU0_TA', symObjAddr: 0x797C, symBinAddr: 0x10001B868, symSize: 0x7C }
  - { offset: 0xB1C85, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MeterTickViewVSgxSg7SwiftUI0E0A2fGRzlWl', symObjAddr: 0x79F8, symBinAddr: 0x10001B8E4, symSize: 0x68 }
  - { offset: 0xB1C99, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MeterTickViewVAC7SwiftUI0E0AAWl', symObjAddr: 0x7A60, symBinAddr: 0x10001B94C, symSize: 0x40 }
  - { offset: 0xB1CAD, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterLabelViewVSgWOy', symObjAddr: 0x7AA0, symBinAddr: 0x10001B98C, symSize: 0x30 }
  - { offset: 0xB1CC1, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterLabelViewVSgWOe', symObjAddr: 0x7AD0, symBinAddr: 0x10001B9BC, symSize: 0x30 }
  - { offset: 0xB1CD5, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA15ModifiedContentVyAA011StrokeShapeD0VyAA6CircleVAA5ColorVAA05EmptyD0VGAA12_FrameLayoutVG_AEyAA01_hD0VyAiA14RadialGradientVGAPGAEyAEyASyAiKGAPGAA16_OverlayModifierVyANGGtGWOr', symObjAddr: 0x7B00, symBinAddr: 0x10001B9EC, symSize: 0x74 }
  - { offset: 0xB1CE9, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA15ModifiedContentVyAA011StrokeShapeD0VyAA6CircleVAA5ColorVAA05EmptyD0VGAA12_FrameLayoutVG_AEyAA01_hD0VyAiA14RadialGradientVGAPGAEyAEyASyAiKGAPGAA16_OverlayModifierVyANGGtGWOs', symObjAddr: 0x7B74, symBinAddr: 0x10001BA60, symSize: 0x74 }
  - { offset: 0xB1CFD, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA10_ShapeViewVyAA6CircleVAA14RadialGradientVGAA12_FrameLayoutVGWOr', symObjAddr: 0x7C1C, symBinAddr: 0x10001BB08, symSize: 0x28 }
  - { offset: 0xB1D11, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA10_ShapeViewVyAA6CircleVAA5ColorVGAA12_FrameLayoutVGAA16_OverlayModifierVyAA06StrokeeF0VyAgiA05EmptyF0VGGGWOr', symObjAddr: 0x7C44, symBinAddr: 0x10001BB30, symSize: 0x48 }
  - { offset: 0xB1D25, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA10_ShapeViewVyAA6CircleVAA5ColorVGAA12_FrameLayoutVGAA16_OverlayModifierVyAA06StrokeeF0VyAgiA05EmptyF0VGGGWOs', symObjAddr: 0x7C8C, symBinAddr: 0x10001BB78, symSize: 0x48 }
  - { offset: 0xB1D39, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA10_ShapeViewVyAA6CircleVAA14RadialGradientVGAA12_FrameLayoutVGWOs', symObjAddr: 0x7CD4, symBinAddr: 0x10001BBC0, symSize: 0x28 }
  - { offset: 0xB1D4D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterLabelViewVwxx', symObjAddr: 0x7D30, symBinAddr: 0x10001BC1C, symSize: 0x28 }
  - { offset: 0xB1D61, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterLabelViewVwcp', symObjAddr: 0x7D58, symBinAddr: 0x10001BC44, symSize: 0x44 }
  - { offset: 0xB1D75, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterLabelViewVwca', symObjAddr: 0x7D9C, symBinAddr: 0x10001BC88, symSize: 0x74 }
  - { offset: 0xB1D89, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterLabelViewVwta', symObjAddr: 0x7E24, symBinAddr: 0x10001BCFC, symSize: 0x4C }
  - { offset: 0xB1D9D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterLabelViewVMa', symObjAddr: 0x7F00, symBinAddr: 0x10001BD48, symSize: 0x10 }
  - { offset: 0xB1DB1, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MeterTickViewVwxx', symObjAddr: 0x7F10, symBinAddr: 0x10001BD58, symSize: 0x8 }
  - { offset: 0xB1DC5, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MeterTickViewVwcp', symObjAddr: 0x7F18, symBinAddr: 0x10001BD60, symSize: 0x3C }
  - { offset: 0xB1DD9, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MeterTickViewVwca', symObjAddr: 0x7F54, symBinAddr: 0x10001BD9C, symSize: 0x64 }
  - { offset: 0xB1DED, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MeterTickViewVwta', symObjAddr: 0x7FB8, symBinAddr: 0x10001BE00, symSize: 0x4C }
  - { offset: 0xB1E01, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MeterTickViewVwet', symObjAddr: 0x8004, symBinAddr: 0x10001BE4C, symSize: 0x48 }
  - { offset: 0xB1E15, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MeterTickViewVwst', symObjAddr: 0x804C, symBinAddr: 0x10001BE94, symSize: 0x48 }
  - { offset: 0xB1E29, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MeterTickViewVMa', symObjAddr: 0x8094, symBinAddr: 0x10001BEDC, symSize: 0x10 }
  - { offset: 0xB1E3D, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6ZStackVyAA9TupleViewVyACyACyAA06_ShapeG0VyAA16RoundedRectangleVAA5ColorVGAA12_FrameLayoutVGAA13_OffsetEffectVG_ACyACyAIyAA6CircleVAMGAPGASGtGGAA09_RotationO0VGACyxq_GAA0G0A2AA4_RzAA0G8ModifierR_rlWl', symObjAddr: 0x80A8, symBinAddr: 0x10001BEF0, symSize: 0x88 }
  - { offset: 0xB1E51, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterLabelViewV7SwiftUI0E0AA4BodyAdEP_AGWT', symObjAddr: 0x8188, symBinAddr: 0x10001BFD0, symSize: 0x10 }
  - { offset: 0xB1E65, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MeterTickViewV7SwiftUI0E0AA4BodyAdEP_AGWT', symObjAddr: 0x8198, symBinAddr: 0x10001BFE0, symSize: 0x10 }
  - { offset: 0xB1E79, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA4TextVAA13_OffsetEffectVGACyxq_GAA4ViewA2aJRzAA0H8ModifierR_rlWl', symObjAddr: 0x8364, symBinAddr: 0x10001C0E4, symSize: 0x68 }
  - { offset: 0xB1E94, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewV4bodyQrvg', symObjAddr: 0x1D8, symBinAddr: 0x1000145C8, symSize: 0x200 }
  - { offset: 0xB1EB8, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewV4bodyQrvg7SwiftUI0E0PAEE19navigationBarHiddenyQrSbFQOyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE6VStackVyAE05TupleE0VyAgEEAIyQrAM_AQtFQOyAE6HStackVyAUyAE4TextV_AE6SpacerVAE6ButtonVyAgEE15foregroundColoryQrAE0Y0VSgFQOyAgEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo_GtGG_Qo__AA016FrequencyDisplayE0VAA0cb5MeterE0VAA010StringInfoE0VSgA_AA014ControlButtonsE0VtGG_Qo__Qo_yXEfU_', symObjAddr: 0x3D8, symBinAddr: 0x1000147C8, symSize: 0x150 }
  - { offset: 0xB1F8B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewV4bodyQrvg7SwiftUI0E0PAEE19navigationBarHiddenyQrSbFQOyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE6VStackVyAE05TupleE0VyAgEEAIyQrAM_AQtFQOyAE6HStackVyAUyAE4TextV_AE6SpacerVAE6ButtonVyAgEE15foregroundColoryQrAE0Y0VSgFQOyAgEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo_GtGG_Qo__AA016FrequencyDisplayE0VAA0cb5MeterE0VAA010StringInfoE0VSgA_AA014ControlButtonsE0VtGG_Qo__Qo_yXEfU_A27_yXEfU_', symObjAddr: 0x528, symBinAddr: 0x100014918, symSize: 0x734 }
  - { offset: 0xB21FB, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewV4bodyQrvg7SwiftUI0E0PAEE19navigationBarHiddenyQrSbFQOyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE6VStackVyAE05TupleE0VyAgEEAIyQrAM_AQtFQOyAE6HStackVyAUyAE4TextV_AE6SpacerVAE6ButtonVyAgEE15foregroundColoryQrAE0Y0VSgFQOyAgEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo_GtGG_Qo__AA016FrequencyDisplayE0VAA0cb5MeterE0VAA010StringInfoE0VSgA_AA014ControlButtonsE0VtGG_Qo__Qo_yXEfU_A27_yXEfU_A15_yXEfU_', symObjAddr: 0xC5C, symBinAddr: 0x10001504C, symSize: 0x2A0 }
  - { offset: 0xB2246, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewV4bodyQrvg7SwiftUI0E0PAEE19navigationBarHiddenyQrSbFQOyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE6VStackVyAE05TupleE0VyAgEEAIyQrAM_AQtFQOyAE6HStackVyAUyAE4TextV_AE6SpacerVAE6ButtonVyAgEE15foregroundColoryQrAE0Y0VSgFQOyAgEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo_GtGG_Qo__AA016FrequencyDisplayE0VAA0cb5MeterE0VAA010StringInfoE0VSgA_AA014ControlButtonsE0VtGG_Qo__Qo_yXEfU_A27_yXEfU_A15_yXEfU_A13_yXEfU0_', symObjAddr: 0xEFC, symBinAddr: 0x1000152EC, symSize: 0x8C }
  - { offset: 0xB229B, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewV4bodyQrvg7SwiftUI0E0PAEE19navigationBarHiddenyQrSbFQOyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE6VStackVyAE05TupleE0VyAgEEAIyQrAM_AQtFQOyAE6HStackVyAUyAE4TextV_AE6SpacerVAE6ButtonVyAgEE15foregroundColoryQrAE0Y0VSgFQOyAgEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo_GtGG_Qo__AA016FrequencyDisplayE0VAA0cb5MeterE0VAA010StringInfoE0VSgA_AA014ControlButtonsE0VtGG_Qo__Qo_yXEfU_A27_yXEfU_yycAA04MainE5ModelCcfu_yycfu0_', symObjAddr: 0xF88, symBinAddr: 0x100015378, symSize: 0x98 }
  - { offset: 0xB231A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewV4bodyQrvgAA08SettingsE0VycfU0_', symObjAddr: 0x1020, symBinAddr: 0x100015410, symSize: 0xA0 }
  - { offset: 0xB3545, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewV7SwiftUI0E0AadEP4body4BodyQzvgTW', symObjAddr: 0x10C0, symBinAddr: 0x1000154B0, symSize: 0x10 }
  - { offset: 0xB357A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewV4bodyQrvg', symObjAddr: 0x1284, symBinAddr: 0x1000155A8, symSize: 0x1B8 }
  - { offset: 0xB3643, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewV4bodyQrvg7SwiftUI05TupleE0VyAA0d10BackgroundE0V_AA0d5ScaleE0VAA0d6NeedleE0VAE0E0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AwE9AlignmentVtFQOyAE06_ShapeE0VyAE6CircleVAE5ColorVG_Qo_AE6VStackVyAGyAE6SpacerV_AoEE7paddingyQrAE4EdgeO3SetV_AWtFQOyAE6HStackVyAGyA7_yAGyAE4TextV_A18_tGG_A20_SgA20_tGG_Qo_tGGtGyXEfU_', symObjAddr: 0x143C, symBinAddr: 0x100015760, symSize: 0x270 }
  - { offset: 0xB378D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewV4bodyQrvg7SwiftUI05TupleE0VyAA0d10BackgroundE0V_AA0d5ScaleE0VAA0d6NeedleE0VAE0E0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AwE9AlignmentVtFQOyAE06_ShapeE0VyAE6CircleVAE5ColorVG_Qo_AE6VStackVyAGyAE6SpacerV_AoEE7paddingyQrAE4EdgeO3SetV_AWtFQOyAE6HStackVyAGyA7_yAGyAE4TextV_A18_tGG_A20_SgA20_tGG_Qo_tGGtGyXEfU_A25_yXEfU_', symObjAddr: 0x16AC, symBinAddr: 0x1000159D0, symSize: 0x190 }
  - { offset: 0xB3868, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewV4bodyQrvg7SwiftUI05TupleE0VyAA0d10BackgroundE0V_AA0d5ScaleE0VAA0d6NeedleE0VAE0E0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AwE9AlignmentVtFQOyAE06_ShapeE0VyAE6CircleVAE5ColorVG_Qo_AE6VStackVyAGyAE6SpacerV_AoEE7paddingyQrAE4EdgeO3SetV_AWtFQOyAE6HStackVyAGyA7_yAGyAE4TextV_A18_tGG_A20_SgA20_tGG_Qo_tGGtGyXEfU_A25_yXEfU_A22_yXEfU_', symObjAddr: 0x183C, symBinAddr: 0x100015B60, symSize: 0x3AC }
  - { offset: 0xB3A26, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewV4bodyQrvg7SwiftUI05TupleE0VyAA0d10BackgroundE0V_AA0d5ScaleE0VAA0d6NeedleE0VAE0E0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AwE9AlignmentVtFQOyAE06_ShapeE0VyAE6CircleVAE5ColorVG_Qo_AE6VStackVyAGyAE6SpacerV_AoEE7paddingyQrAE4EdgeO3SetV_AWtFQOyAE6HStackVyAGyA7_yAGyAE4TextV_A18_tGG_A20_SgA20_tGG_Qo_tGGtGyXEfU_A25_yXEfU_A22_yXEfU_A19_yXEfU_', symObjAddr: 0x1BE8, symBinAddr: 0x100015F0C, symSize: 0x2E4 }
  - { offset: 0xB3AD2, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewV4bodyQrvg7SwiftUI05TupleE0VyAA0d10BackgroundE0V_AA0d5ScaleE0VAA0d6NeedleE0VAE0E0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AwE9AlignmentVtFQOyAE06_ShapeE0VyAE6CircleVAE5ColorVG_Qo_AE6VStackVyAGyAE6SpacerV_AoEE7paddingyQrAE4EdgeO3SetV_AWtFQOyAE6HStackVyAGyA7_yAGyAE4TextV_A18_tGG_A20_SgA20_tGG_Qo_tGGtGyXEfU_A25_yXEfU_A22_yXEfU_A19_yXEfU0_', symObjAddr: 0x1ECC, symBinAddr: 0x1000161F0, symSize: 0x2E4 }
  - { offset: 0xB3B7E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewV4bodyQrvg7SwiftUI05TupleE0VyAA0d10BackgroundE0V_AA0d5ScaleE0VAA0d6NeedleE0VAE0E0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AwE9AlignmentVtFQOyAE06_ShapeE0VyAE6CircleVAE5ColorVG_Qo_AE6VStackVyAGyAE6SpacerV_AoEE7paddingyQrAE4EdgeO3SetV_AWtFQOyAE6HStackVyAGyA7_yAGyAE4TextV_A18_tGG_A20_SgA20_tGG_Qo_tGGtGyXEfU_A25_yXEfU_A22_yXEfU_A19_yXEfU1_', symObjAddr: 0x21B0, symBinAddr: 0x1000164D4, symSize: 0x35C }
  - { offset: 0xB3C4A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewV4bodyQrvgySf_SftcfU0_', symObjAddr: 0x250C, symBinAddr: 0x100016830, symSize: 0xAC }
  - { offset: 0xB3DD2, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner012ProfessionalB9MeterViewV7SwiftUI0E0AadEP4body4BodyQzvgTW', symObjAddr: 0x25D8, symBinAddr: 0x1000168DC, symSize: 0x40 }
  - { offset: 0xB3DFF, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner19MeterBackgroundViewV4bodyQrvg7SwiftUI05TupleE0VyAE0E0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AqE9AlignmentVtFQOyAE011StrokeShapeE0VyAE6CircleVAE5ColorVAE05EmptyE0VG_Qo__AiEEAjklMQrAQ_AqStFQOyAE01_sE0VyAwE14RadialGradientVG_Qo_AiEE7overlay_AMQrqd___AStAeHRd__lFQOyAiEEAjklMQrAQ_AqStFQOyA3_yAwYG_Qo__A0_Qo_tGyXEfU_', symObjAddr: 0x2618, symBinAddr: 0x10001691C, symSize: 0x5B4 }
  - { offset: 0xB4130, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner19MeterBackgroundViewV7SwiftUI0E0AadEP4body4BodyQzvgTW', symObjAddr: 0x2BCC, symBinAddr: 0x100016ED0, symSize: 0x94 }
  - { offset: 0xB41CF, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterScaleViewV4bodyQrvg7SwiftUI05TupleE0VyAE7ForEachVySNySiGSiAGyAA0c4TickE0V_AA0c5LabelE0VSgtGG_AIyAJSiALSgGtGyXEfU_', symObjAddr: 0x2C60, symBinAddr: 0x100016F64, symSize: 0x2B8 }
  - { offset: 0xB4208, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterScaleViewV4bodyQrvg7SwiftUI05TupleE0VyAE7ForEachVySNySiGSiAGyAA0c4TickE0V_AA0c5LabelE0VSgtGG_AIyAJSiALSgGtGyXEfU_APSicfU_', symObjAddr: 0x2F18, symBinAddr: 0x10001721C, symSize: 0x1C0 }
  - { offset: 0xB4355, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterScaleViewV7SwiftUI0E0AadEP4body4BodyQzvgTW', symObjAddr: 0x30D8, symBinAddr: 0x1000173DC, symSize: 0x54 }
  - { offset: 0xB43F8, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MeterTickViewV4bodyQrvg', symObjAddr: 0x312C, symBinAddr: 0x100017430, symSize: 0xDC }
  - { offset: 0xB44C7, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner13MeterTickViewV7SwiftUI0E0AadEP4body4BodyQzvgTW', symObjAddr: 0x3208, symBinAddr: 0x10001750C, symSize: 0x4 }
  - { offset: 0xB44E8, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterLabelViewV4bodyQrvg', symObjAddr: 0x320C, symBinAddr: 0x100017510, symSize: 0x198 }
  - { offset: 0xB45A1, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14MeterLabelViewV7SwiftUI0E0AadEP4body4BodyQzvgTW', symObjAddr: 0x33A4, symBinAddr: 0x1000176A8, symSize: 0x4 }
  - { offset: 0xB45F7, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15MeterNeedleViewV4bodyQrvg7SwiftUI05TupleE0VyAE0E0PAEE6offset1x1yQr12CoreGraphics7CGFloatV_AOtFQOyAiEE5frame5width6height9alignmentQrAOSg_AtE9AlignmentVtFQOyAE06_ShapeE0VyAE16RoundedRectangleVAE5ColorVG_Qo__Qo__AiEEAjkLQrAO_AOtFQOyAiEEApqrSQrAT_AtVtFQOyAXyAE6CircleVA0_G_Qo__Qo_tGyXEfU_', symObjAddr: 0x33A8, symBinAddr: 0x1000176AC, symSize: 0x3DC }
  - { offset: 0xB47F5, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner15MeterNeedleViewV7SwiftUI0E0AadEP4body4BodyQzvgTW', symObjAddr: 0x3784, symBinAddr: 0x100017A88, symSize: 0xE8 }
  - { offset: 0xB48EE, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14StringInfoViewV4bodyQrvg', symObjAddr: 0x386C, symBinAddr: 0x100017B70, symSize: 0x368 }
  - { offset: 0xB4A55, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14StringInfoViewV4bodyQrvg7SwiftUI05TupleE0VyAE4TextV_AE6HStackVyAGyAI_A2ItGGtGyXEfU_', symObjAddr: 0x3BD4, symBinAddr: 0x100017ED8, symSize: 0x2C8 }
  - { offset: 0xB4B0A, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14StringInfoViewV4bodyQrvg7SwiftUI05TupleE0VyAE4TextV_AE6HStackVyAGyAI_A2ItGGtGyXEfU_ALyXEfU_', symObjAddr: 0x3E9C, symBinAddr: 0x1000181A0, symSize: 0x528 }
  - { offset: 0xB4C63, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner14StringInfoViewV7SwiftUI0E0AadEP4body4BodyQzvgTW', symObjAddr: 0x43C4, symBinAddr: 0x1000186C8, symSize: 0x10 }
  - { offset: 0xB4C98, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewV4bodyQrvg7SwiftUI0D0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationM4ItemV0noP0OFQOyAgEE0lN0yQrAE18LocalizedStringKeyVFQOyAE6VStackVyAE05TupleD0VyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE4TextV_Qo__AE6SpacerVtGG_Qo__Qo__AE0jK7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0jR0VyytAE6ButtonVyA5_GGQo_Qo_yXEfU_', symObjAddr: 0x43D4, symBinAddr: 0x1000186D8, symSize: 0x384 }
  - { offset: 0xB4D1E, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewV4bodyQrvg7SwiftUI0D0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationM4ItemV0noP0OFQOyAgEE0lN0yQrAE18LocalizedStringKeyVFQOyAE6VStackVyAE05TupleD0VyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE4TextV_Qo__AE6SpacerVtGG_Qo__Qo__AE0jK7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0jR0VyytAE6ButtonVyA5_GGQo_Qo_yXEfU_A9_yXEfU_', symObjAddr: 0x4758, symBinAddr: 0x100018A5C, symSize: 0x180 }
  - { offset: 0xB4DEA, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewV4bodyQrvg7SwiftUI0D0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationM4ItemV0noP0OFQOyAgEE0lN0yQrAE18LocalizedStringKeyVFQOyAE6VStackVyAE05TupleD0VyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE4TextV_Qo__AE6SpacerVtGG_Qo__Qo__AE0jK7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0jR0VyytAE6ButtonVyA5_GGQo_Qo_yXEfU_A22_yXEfU0_', symObjAddr: 0x48D8, symBinAddr: 0x100018BDC, symSize: 0x128 }
  - { offset: 0xB4E21, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewV4bodyQrvg7SwiftUI0D0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationM4ItemV0noP0OFQOyAgEE0lN0yQrAE18LocalizedStringKeyVFQOyAE6VStackVyAE05TupleD0VyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE4TextV_Qo__AE6SpacerVtGG_Qo__Qo__AE0jK7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0jR0VyytAE6ButtonVyA5_GGQo_Qo_yXEfU_A22_yXEfU0_A20_yXEfU_', symObjAddr: 0x4A00, symBinAddr: 0x100018D04, symSize: 0xF8 }
  - { offset: 0xB4E47, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewV4bodyQrvg7SwiftUI0D0PAEE7toolbar7contentQrqd__yXE_tAE14ToolbarContentRd__lFQOyAgEE29navigationBarTitleDisplayModeyQrAE010NavigationM4ItemV0noP0OFQOyAgEE0lN0yQrAE18LocalizedStringKeyVFQOyAE6VStackVyAE05TupleD0VyAgEE7paddingyQrAE4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAE4TextV_Qo__AE6SpacerVtGG_Qo__Qo__AE0jK7BuilderV10buildBlockyQrxAeJRzlFZQOy_AE0jR0VyytAE6ButtonVyA5_GGQo_Qo_yXEfU_A22_yXEfU0_A20_yXEfU_yyScMYccfU_', symObjAddr: 0x4AF8, symBinAddr: 0x100018DFC, symSize: 0x88 }
  - { offset: 0xB4E8D, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner12SettingsViewV7SwiftUI0D0AadEP4body4BodyQzvgTW', symObjAddr: 0x4B80, symBinAddr: 0x100018E84, symSize: 0x15C }
  - { offset: 0xB4EC3, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner22ProfessionalTuningViewV18getTargetFrequency33_0D9BF467B77FBB78DFB3F2C723072EC3LLSfyFTf4x_nTf4dn_n', symObjAddr: 0x4D3C, symBinAddr: 0x100019040, symSize: 0x12C }
  - { offset: 0xB50D2, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner19ResourceBundleClass026_C676AE7C85AE83B5C0D2BAA28L5D5013LLCfD', symObjAddr: 0x0, symBinAddr: 0x10001C15C, symSize: 0x10 }
  - { offset: 0xB5114, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner19ResourceBundleClass026_C676AE7C85AE83B5C0D2BAA28L5D5013LLCMa', symObjAddr: 0x10, symBinAddr: 0x10001C16C, symSize: 0x20 }
  - { offset: 0xB512F, size: 0x8, addend: 0x0, symName: '_$s11GuitarTuner19ResourceBundleClass026_C676AE7C85AE83B5C0D2BAA28L5D5013LLCfD', symObjAddr: 0x0, symBinAddr: 0x10001C15C, symSize: 0x10 }
...
