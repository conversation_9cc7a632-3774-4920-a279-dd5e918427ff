{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "df1f89dba0f9bac951f1117feebc2816f3a26d0a7c543038979e4e746d13e14d", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SWIFT_COMPILATION_MODE": "wholemodule"}, "guid": "df1f89dba0f9bac951f1117feebc281690d761582d9a1a82842b7c35822b0f78", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "df1f89dba0f9bac951f1117feebc281683d6ccde83b21986e73bc9c5e1b4c026", "path": "GuitarModels.swift", "sourceTree": "<group>", "type": "file"}], "guid": "df1f89dba0f9bac951f1117feebc28161838b375f2cfeee137f4cd7c769d4e62", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "df1f89dba0f9bac951f1117feebc28163586e5f732391e642b0ebd20ca476f8c", "path": "AudioManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "df1f89dba0f9bac951f1117feebc2816bb96c341e56b1556ddacacc0354f9e15", "path": "SettingsManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "df1f89dba0f9bac951f1117feebc2816eade967fd8baeec75454cca352c7f83a", "path": "TuningAnalyzer.swift", "sourceTree": "<group>", "type": "file"}], "guid": "df1f89dba0f9bac951f1117feebc281659e9547b94d4e6eeb2f135bcd5a23c7c", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "df1f89dba0f9bac951f1117feebc28163e0440c62c03dd31d38d046ed38894e5", "path": "MainViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "df1f89dba0f9bac951f1117feebc281681a4de47e350f41d92f3c446f57ada94", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "df1f89dba0f9bac951f1117feebc2816a1f647525441b551d722d335dc0828d4", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "df1f89dba0f9bac951f1117feebc281687ca5c71c36a293dace7f602e1b5f840", "path": "ProfessionalTuningView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "df1f89dba0f9bac951f1117feebc281603be9c0b1f782fb3f7c3f41af6674628", "path": "QuickTuningView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "df1f89dba0f9bac951f1117feebc2816c2b05c315446aedf099113e499cc6e29", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "df1f89dba0f9bac951f1117feebc2816da3bb5c8c8daf5a101406a332aab0242", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "df1f89dba0f9bac951f1117feebc2816d537d96425bcef90435087f58feffa07", "path": "GuitarTuner.entitlements", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "df1f89dba0f9bac951f1117feebc28163f7267e98d5ab415d237ec789bceabe9", "path": "GuitarTunerApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "df1f89dba0f9bac951f1117feebc28162c590c01039a4040ecec401511d49b6c", "name": "GuitarTuner", "path": "GuitarTuner", "sourceTree": "<group>", "type": "group"}, {"guid": "df1f89dba0f9bac951f1117feebc28161245d86b91a4ae8e88749ea11c12c045", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "df1f89dba0f9bac951f1117feebc28166e9716869e7d3963a4db9fbf0b552199", "name": "GuitarTuner", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "df1f89dba0f9bac951f1117feebc2816", "path": "/Users/<USER>/Documents/dev/code/app/guitar_tuner/GuitarTuner.xcodeproj", "projectDirectory": "/Users/<USER>/Documents/dev/code/app/guitar_tuner", "targets": ["TARGET@v11_hash=9d332b658a72821c7e751a3903ed21b7"]}