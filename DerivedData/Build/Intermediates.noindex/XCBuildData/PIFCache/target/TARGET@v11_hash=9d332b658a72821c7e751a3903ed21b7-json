{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_ENTITLEMENTS": "GuitarTuner/GuitarTuner.entitlements", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "XZ2V6XWLW8", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_NSMicrophoneUsageDescription": "此应用需要访问麦克风来检测吉他音频并进行调音分析", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]": "YES", "INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]": "UIStatusBarStyleDefault", "INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]": "UIStatusBarStyleDefault", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "IPHONEOS_DEPLOYMENT_TARGET": "17.6", "LD_RUNPATH_SEARCH_PATHS": "@executable_path/Frameworks", "LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]": "@executable_path/../Frameworks", "MACOSX_DEPLOYMENT_TARGET": "15.0", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com..GuitarTuner", "PRODUCT_NAME": "$(TARGET_NAME)", "REGISTER_APP_GROUPS": "YES", "SDKROOT": "auto", "SUPPORTED_PLATFORMS": "iphoneos iphonesimulator macosx xros xrsimulator", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2,7", "XROS_DEPLOYMENT_TARGET": "2.5"}, "guid": "df1f89dba0f9bac951f1117feebc2816312a2cef4a17cb82120021c33fa27e92", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_ENTITLEMENTS": "GuitarTuner/GuitarTuner.entitlements", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "XZ2V6XWLW8", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_NSMicrophoneUsageDescription": "此应用需要访问麦克风来检测吉他音频并进行调音分析", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]": "YES", "INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]": "UIStatusBarStyleDefault", "INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]": "UIStatusBarStyleDefault", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "IPHONEOS_DEPLOYMENT_TARGET": "17.6", "LD_RUNPATH_SEARCH_PATHS": "@executable_path/Frameworks", "LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]": "@executable_path/../Frameworks", "MACOSX_DEPLOYMENT_TARGET": "15.0", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com..GuitarTuner", "PRODUCT_NAME": "$(TARGET_NAME)", "REGISTER_APP_GROUPS": "YES", "SDKROOT": "auto", "SUPPORTED_PLATFORMS": "iphoneos iphonesimulator macosx xros xrsimulator", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2,7", "XROS_DEPLOYMENT_TARGET": "2.5"}, "guid": "df1f89dba0f9bac951f1117feebc2816e9e121eb7280bad12d2cc5e653cb9b72", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "df1f89dba0f9bac951f1117feebc2816bb96c341e56b1556ddacacc0354f9e15", "guid": "df1f89dba0f9bac951f1117feebc28164989901bdacba5a093f628be7add2798"}, {"fileReference": "df1f89dba0f9bac951f1117feebc281683d6ccde83b21986e73bc9c5e1b4c026", "guid": "df1f89dba0f9bac951f1117feebc281689f34dcc0392dc6b2b06553c422df994"}, {"fileReference": "df1f89dba0f9bac951f1117feebc28163586e5f732391e642b0ebd20ca476f8c", "guid": "df1f89dba0f9bac951f1117feebc281600dd5799874cbb67ebbdfddf5bc3ca1c"}, {"fileReference": "df1f89dba0f9bac951f1117feebc2816eade967fd8baeec75454cca352c7f83a", "guid": "df1f89dba0f9bac951f1117feebc2816861935d9f2913645dda861106a1893be"}, {"fileReference": "df1f89dba0f9bac951f1117feebc281603be9c0b1f782fb3f7c3f41af6674628", "guid": "df1f89dba0f9bac951f1117feebc281670117d76503fb42c5ae307a1bd4c0f26"}, {"fileReference": "df1f89dba0f9bac951f1117feebc28163e0440c62c03dd31d38d046ed38894e5", "guid": "df1f89dba0f9bac951f1117feebc2816beaeb5be4d01b7336f92b8ce1e1467c8"}, {"fileReference": "df1f89dba0f9bac951f1117feebc2816a1f647525441b551d722d335dc0828d4", "guid": "df1f89dba0f9bac951f1117feebc28160a2dcb27aee714c0c9e7f28f524974e8"}, {"fileReference": "df1f89dba0f9bac951f1117feebc28163f7267e98d5ab415d237ec789bceabe9", "guid": "df1f89dba0f9bac951f1117feebc2816432219379d8eafd97477d332e39f6b44"}, {"fileReference": "df1f89dba0f9bac951f1117feebc281687ca5c71c36a293dace7f602e1b5f840", "guid": "df1f89dba0f9bac951f1117feebc2816f13031bbbf0a701217f08832ed66ec60"}], "guid": "df1f89dba0f9bac951f1117feebc28164f930cd4e5ccc3eb8271d5f39e8bfd75", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "df1f89dba0f9bac951f1117feebc2816b094cc8e7a3e58e47f6b6f035cca9989", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "df1f89dba0f9bac951f1117feebc2816da3bb5c8c8daf5a101406a332aab0242", "guid": "df1f89dba0f9bac951f1117feebc2816f3f8c22f917a7daacb58215f6953d179"}], "guid": "df1f89dba0f9bac951f1117feebc2816ed565de0b1d3e750d4c5ff443ff6fa4c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "df1f89dba0f9bac951f1117feebc2816a0ecad31cd7996d46f3391ddc4b044e6", "name": "GuitarTuner", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "df1f89dba0f9bac951f1117feebc2816c0c3a50220fbee854982f2804078eb70", "name": "GuitarTuner.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}